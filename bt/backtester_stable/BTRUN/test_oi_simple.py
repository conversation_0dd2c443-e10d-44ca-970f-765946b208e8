#!/usr/bin/env python3
"""
Simple OI System Test
====================

This script performs basic testing of the OI system to validate functionality.
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime, date

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_imports():
    """Test basic imports."""
    logger.info("Testing basic imports...")
    
    try:
        # Test basic imports
        import pandas as pd
        logger.info("✓ pandas imported")
        
        import numpy as np
        logger.info("✓ numpy imported")
        
        # Test local imports
        from BT_OI_GPU import run_oi_strategy
        logger.info("✓ BT_OI_GPU imported")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Import failed: {e}")
        return False

def test_input_sheets():
    """Test input sheet validation."""
    logger.info("Testing input sheet validation...")
    
    try:
        input_path = Path('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi')
        
        # Check if input sheets exist
        legacy_file = input_path / 'input_maxoi.xlsx'
        settings_file = input_path / 'bt_setting.xlsx'
        
        if legacy_file.exists():
            logger.info(f"✓ Legacy file found: {legacy_file}")
        else:
            logger.warning(f"✗ Legacy file not found: {legacy_file}")
            
        if settings_file.exists():
            logger.info(f"✓ Settings file found: {settings_file}")
        else:
            logger.warning(f"✗ Settings file not found: {settings_file}")
            
        return True
        
    except Exception as e:
        logger.error(f"✗ Input sheet validation failed: {e}")
        return False

def test_database_connection():
    """Test database connection."""
    logger.info("Testing database connection...")
    
    try:
        from heavydb_connection import HeavyDBConnection
        
        # Test connection
        conn = HeavyDBConnection()
        if conn.test_connection():
            logger.info("✓ HeavyDB connection successful")
            return True
        else:
            logger.error("✗ HeavyDB connection failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Database connection test failed: {e}")
        return False

def test_oi_basic_functionality():
    """Test basic OI functionality."""
    logger.info("Testing basic OI functionality...")
    
    try:
        # Test with minimal parameters
        from BT_OI_GPU import run_oi_strategy
        
        # Create minimal test configuration
        test_config = {
            'start_date': date(2024, 4, 1),
            'end_date': date(2024, 4, 2),  # Just 1 day for quick test
            'index': 'NIFTY',
            'strike_method': 'MAXOI_1',
            'oi_threshold': 800000,
            'timeframe': 3,
            'max_open_positions': 1
        }
        
        logger.info("✓ Basic OI configuration created")
        logger.info(f"Test config: {test_config}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Basic OI functionality test failed: {e}")
        return False

def run_simple_tests():
    """Run all simple tests."""
    logger.info("Starting Simple OI System Tests...")
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Input Sheets", test_input_sheets),
        ("Database Connection", test_database_connection),
        ("OI Basic Functionality", test_oi_basic_functionality)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results[test_name] = "PASSED" if result else "FAILED"
            if result:
                passed += 1
                logger.info(f"✓ {test_name}: PASSED")
            else:
                logger.error(f"✗ {test_name}: FAILED")
        except Exception as e:
            results[test_name] = f"ERROR: {e}"
            logger.error(f"✗ {test_name}: ERROR - {e}")
    
    # Summary
    logger.info(f"\n=== TEST SUMMARY ===")
    logger.info(f"Tests Passed: {passed}/{total}")
    logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
    
    for test_name, result in results.items():
        status_icon = "✓" if result == "PASSED" else "✗"
        logger.info(f"{status_icon} {test_name}: {result}")
    
    return passed == total

if __name__ == '__main__':
    success = run_simple_tests()
    sys.exit(0 if success else 1)
