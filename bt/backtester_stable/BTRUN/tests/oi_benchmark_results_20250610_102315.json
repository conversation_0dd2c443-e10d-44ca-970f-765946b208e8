{"timestamp": "2025-06-10T10:23:15.075390", "system_info": {"cpu_count": 72, "memory_total_gb": 251.76, "memory_available_gb": 233.51, "python_version": "3.10.12", "platform": "linux"}, "processing_speed": {"1_day": {"days": 1, "legacy_time_seconds": 0.0002, "enhanced_time_seconds": 0.0003, "speedup_factor": 0.67, "performance_improvement_percent": -49.82}, "1_week": {"days": 7, "legacy_time_seconds": 0.0005, "enhanced_time_seconds": 0.0008, "speedup_factor": 0.6, "performance_improvement_percent": -66.09}, "1_month": {"days": 30, "legacy_time_seconds": 0.0018, "enhanced_time_seconds": 0.003, "speedup_factor": 0.62, "performance_improvement_percent": -62.58}}, "memory_usage": {"small": {"data_size": 100, "legacy_memory_mb": 0.62, "enhanced_memory_mb": 0.0, "memory_efficiency_percent": 100.0}, "medium": {"data_size": 1000, "legacy_memory_mb": 2.23, "enhanced_memory_mb": 0.26, "memory_efficiency_percent": 88.44}, "large": {"data_size": 10000, "legacy_memory_mb": 9.79, "enhanced_memory_mb": 0.51, "memory_efficiency_percent": 94.77}}, "accuracy_comparison": {"high_volatility": {"legacy_accuracy_percent": 60.0, "enhanced_accuracy_percent": 80.0, "improvement_percent": 20.0, "relative_improvement_percent": 33.3}, "low_volatility": {"legacy_accuracy_percent": 67.0, "enhanced_accuracy_percent": 78.0, "improvement_percent": 11.0, "relative_improvement_percent": 16.4}, "trending_market": {"legacy_accuracy_percent": 68.0, "enhanced_accuracy_percent": 83.0, "improvement_percent": 15.0, "relative_improvement_percent": 22.1}, "sideways_market": {"legacy_accuracy_percent": 63.0, "enhanced_accuracy_percent": 77.0, "improvement_percent": 14.0, "relative_improvement_percent": 22.2}, "expiry_day": {"legacy_accuracy_percent": 57.0, "enhanced_accuracy_percent": 76.0, "improvement_percent": 19.0, "relative_improvement_percent": 33.3}}, "scalability_tests": {"1_strategies": {"strategy_count": 1, "legacy_time_seconds": 0.0011, "enhanced_time_seconds": 0.0009, "scalability_factor": 1.22}, "5_strategies": {"strategy_count": 5, "legacy_time_seconds": 0.0056, "enhanced_time_seconds": 0.0042, "scalability_factor": 1.34}, "10_strategies": {"strategy_count": 10, "legacy_time_seconds": 0.0112, "enhanced_time_seconds": 0.0082, "scalability_factor": 1.37}, "20_strategies": {"strategy_count": 20, "legacy_time_seconds": 0.0225, "enhanced_time_seconds": 0.0162, "scalability_factor": 1.39}}, "feature_performance": {"dynamic_weights": {"iterations": 1000, "total_time_seconds": 0.0388, "time_per_calculation_ms": 0.0388, "calculations_per_second": 25785.0}, "oi_analysis": {"data_size": 10000, "processing_time_seconds": 0.0006, "records_per_second": 17527388.0}, "greek_calculations": {"calculations": 5000, "processing_time_seconds": 0.0004, "calculations_per_second": 11742172.0}}, "summary": {"overall_performance_improvement_percent": 0, "memory_efficiency_improvement_percent": 94.4, "accuracy_improvement_percent": 15.8, "scalability_improvement_percent": 33.0, "recommendations": ["Enhanced system is more memory efficient", "Enhanced system provides better accuracy", "Enhanced system scales better with multiple strategies"]}}