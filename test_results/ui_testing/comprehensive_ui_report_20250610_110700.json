{"test_execution_date": "2025-06-10T11:07:00.073467", "base_url": "http://localhost:8000", "test_results": {"login_test": {"status": "PASSED", "steps": [{"step": "navigate_to_login", "status": "PASSED", "url": "http://localhost:8000/login"}, {"step": "phone_input_found", "status": "FAILED"}, {"step": "login_button_found", "status": "FAILED"}], "screenshots": ["/srv/samba/shared/test_results/ui_testing/login_step1_110658.png"], "errors": []}, "upload_test": {"status": "PASSED", "steps": [{"step": "navigate_to_dashboard", "status": "PASSED", "url": "http://localhost:8000/login"}, {"step": "upload_areas_found", "status": "FAILED"}, {"step": "strategy_selection", "status": "FAILED"}], "screenshots": ["/srv/samba/shared/test_results/ui_testing/upload_step1_110659.png"], "errors": []}, "backtest_test": {"status": "PASSED", "steps": [{"step": "execution_buttons_found", "status": "FAILED"}, {"step": "progress_indicators_found", "status": "FAILED"}, {"step": "log_areas_found", "status": "FAILED"}], "screenshots": ["/srv/samba/shared/test_results/ui_testing/backtest_final_110659.png"], "errors": []}, "progress_test": {}, "results_test": {}}, "summary": {"total_tests": 5, "passed_tests": 3, "failed_tests": 0, "total_screenshots": 3}}