# TV Strategy Validation Plan (Section 3.2)

**Date**: June 10, 2025  
**Status**: IN PROGRESS

## Overview

TV (TradingView) strategy validation involves verifying that signal-based trading from external sources works correctly in the GPU system with proper golden format output.

## TV Strategy Components

### 1. Input Structure
- **Portfolio File**: Standard portfolio settings
- **Strategy File**: Signal-based entries/exits
- **Signal Format**: Date, Time, Action (BUY/SELL), Symbol, Quantity

### 2. Key Differences from TBS
- **Entry Method**: Signal-based vs rule-based
- **Exit Logic**: Signal-driven vs SL/TGT
- **Position Management**: Direct signal execution

## Validation Steps

### Step 1: Analyze TV Input Format
- Check existing TV input files
- Understand signal structure
- Verify column mappings

### Step 2: Create TV Test Scenarios
1. **Basic Long/Short Signals**
   - Simple BUY/SELL signals
   - Verify position entry/exit

2. **Multiple Signals Per Day**
   - Test signal ordering
   - Verify execution timing

3. **Complex Signal Patterns**
   - Pyramiding positions
   - Partial exits
   - Stop/Target signals

### Step 3: Run TV Backtests
- Execute GPU system with TV strategies
- Generate golden format output
- Verify signal execution

### Step 4: Validate Output
- Check signal mapping to trades
- Verify execution prices
- Validate P&L calculations

## Current Status

1. **Golden Format**: ✅ Implemented for TV strategies
2. **Signal Parsing**: Need to verify
3. **Execution Logic**: Need to test
4. **Output Validation**: Pending

## Next Actions

1. Find and analyze existing TV input files
2. Create comprehensive TV test cases
3. Run TV backtests through GPU system
4. Compare with expected results
5. Document findings