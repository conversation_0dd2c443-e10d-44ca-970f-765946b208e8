#!/usr/bin/env python3
"""
Input Sheets Organization Script
===============================

This script organizes input sheets for clean testing:
1. Creates backup folders for old input sheets
2. Organizes current input sheets by format type
3. Validates input sheet integrity
4. Creates clean testing environment

Usage:
    python organize_input_sheets.py --backup-old
    python organize_input_sheets.py --validate-sheets
    python organize_input_sheets.py --create-clean-structure
"""

import os
import sys
import argparse
import logging
import shutil
from pathlib import Path
from datetime import datetime
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InputSheetOrganizer:
    """Organizes input sheets for clean testing environment."""
    
    def __init__(self):
        self.base_path = Path('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets')
        self.oi_path = self.base_path / 'oi'
        self.backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Define file categories
        self.file_categories = {
            'legacy': [
                'input_maxoi.xlsx',
                'bt_setting.xlsx'
            ],
            'enhanced': [
                'input_enhanced_oi_config.xlsx',
                'bt_setting_enhanced.xlsx'
            ],
            'hybrid': [
                'input_oi_portfolio.xlsx'
            ],
            'documentation': [
                'column_mapping_ml_oi.md',
                'create_enhanced_oi_templates.py'
            ]
        }
    
    def create_backup_structure(self):
        """Create backup folder structure."""
        logger.info("Creating backup folder structure...")
        
        try:
            # Create main backup folder
            backup_path = self.oi_path / 'old_backup'
            backup_path.mkdir(exist_ok=True)
            
            # Create timestamped backup
            timestamped_backup = backup_path / f'backup_{self.backup_timestamp}'
            timestamped_backup.mkdir(exist_ok=True)
            
            logger.info(f"✓ Created backup structure: {timestamped_backup}")
            return timestamped_backup
            
        except Exception as e:
            logger.error(f"Failed to create backup structure: {e}")
            return None
    
    def backup_old_files(self):
        """Backup old input files."""
        logger.info("Backing up old input files...")
        
        backup_path = self.create_backup_structure()
        if not backup_path:
            return False
        
        try:
            files_backed_up = 0
            
            # Backup legacy files
            for file_name in self.file_categories['legacy']:
                source_file = self.oi_path / file_name
                if source_file.exists():
                    dest_file = backup_path / file_name
                    shutil.copy2(source_file, dest_file)
                    logger.info(f"✓ Backed up: {file_name}")
                    files_backed_up += 1
                else:
                    logger.warning(f"File not found for backup: {file_name}")
            
            # Create backup manifest
            manifest_file = backup_path / 'backup_manifest.txt'
            with open(manifest_file, 'w') as f:
                f.write(f"Backup created: {datetime.now().isoformat()}\n")
                f.write(f"Files backed up: {files_backed_up}\n")
                f.write(f"Source path: {self.oi_path}\n")
                f.write("\nFiles included:\n")
                for file_name in self.file_categories['legacy']:
                    source_file = self.oi_path / file_name
                    status = "✓" if source_file.exists() else "✗"
                    f.write(f"{status} {file_name}\n")
            
            logger.info(f"✓ Backup completed: {files_backed_up} files backed up")
            logger.info(f"✓ Backup manifest created: {manifest_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def validate_input_sheets(self):
        """Validate integrity of input sheets."""
        logger.info("Validating input sheet integrity...")
        
        validation_results = {
            'legacy': {},
            'enhanced': {},
            'hybrid': {},
            'documentation': {}
        }
        
        try:
            for category, files in self.file_categories.items():
                logger.info(f"\nValidating {category} files...")
                
                for file_name in files:
                    file_path = self.oi_path / file_name
                    
                    if not file_path.exists():
                        validation_results[category][file_name] = {
                            'exists': False,
                            'error': 'File not found'
                        }
                        logger.warning(f"✗ {file_name}: File not found")
                        continue
                    
                    # Validate Excel files
                    if file_name.endswith('.xlsx'):
                        try:
                            # Try to read Excel file
                            xl = pd.ExcelFile(file_path)
                            sheets = xl.sheet_names
                            
                            validation_results[category][file_name] = {
                                'exists': True,
                                'readable': True,
                                'sheets': sheets,
                                'sheet_count': len(sheets),
                                'file_size': file_path.stat().st_size
                            }
                            
                            logger.info(f"✓ {file_name}: Valid Excel file ({len(sheets)} sheets)")
                            
                        except Exception as e:
                            validation_results[category][file_name] = {
                                'exists': True,
                                'readable': False,
                                'error': str(e)
                            }
                            logger.error(f"✗ {file_name}: Excel validation failed - {e}")
                    
                    # Validate other files
                    else:
                        validation_results[category][file_name] = {
                            'exists': True,
                            'file_size': file_path.stat().st_size
                        }
                        logger.info(f"✓ {file_name}: File exists")
            
            # Generate validation report
            self.generate_validation_report(validation_results)
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return None
    
    def generate_validation_report(self, validation_results):
        """Generate validation report."""
        report_file = self.oi_path / f'validation_report_{self.backup_timestamp}.txt'
        
        try:
            with open(report_file, 'w') as f:
                f.write("Input Sheet Validation Report\n")
                f.write("=" * 40 + "\n")
                f.write(f"Generated: {datetime.now().isoformat()}\n")
                f.write(f"Path: {self.oi_path}\n\n")
                
                for category, files in validation_results.items():
                    f.write(f"{category.upper()} FILES:\n")
                    f.write("-" * 20 + "\n")
                    
                    for file_name, result in files.items():
                        status = "✓" if result.get('exists', False) else "✗"
                        f.write(f"{status} {file_name}\n")
                        
                        if result.get('readable') is False:
                            f.write(f"    Error: {result.get('error', 'Unknown error')}\n")
                        elif result.get('sheets'):
                            f.write(f"    Sheets: {', '.join(result['sheets'])}\n")
                        
                        if result.get('file_size'):
                            f.write(f"    Size: {result['file_size']:,} bytes\n")
                        
                        f.write("\n")
                    
                    f.write("\n")
            
            logger.info(f"✓ Validation report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to generate validation report: {e}")
    
    def create_clean_structure(self):
        """Create clean folder structure for testing."""
        logger.info("Creating clean folder structure...")
        
        try:
            # Create format-specific folders
            folders_to_create = [
                'legacy_format',
                'enhanced_format', 
                'hybrid_format',
                'test_outputs',
                'documentation'
            ]
            
            for folder_name in folders_to_create:
                folder_path = self.oi_path / folder_name
                folder_path.mkdir(exist_ok=True)
                logger.info(f"✓ Created folder: {folder_name}")
            
            # Copy files to appropriate folders
            self.organize_files_by_format()
            
            # Create README files
            self.create_readme_files()
            
            logger.info("✓ Clean structure created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create clean structure: {e}")
            return False
    
    def organize_files_by_format(self):
        """Organize files by format type."""
        logger.info("Organizing files by format...")
        
        try:
            for category, files in self.file_categories.items():
                if category == 'documentation':
                    target_folder = self.oi_path / 'documentation'
                else:
                    target_folder = self.oi_path / f'{category}_format'
                
                for file_name in files:
                    source_file = self.oi_path / file_name
                    target_file = target_folder / file_name
                    
                    if source_file.exists() and not target_file.exists():
                        shutil.copy2(source_file, target_file)
                        logger.info(f"✓ Copied {file_name} to {category}_format/")
            
        except Exception as e:
            logger.error(f"Failed to organize files: {e}")
    
    def create_readme_files(self):
        """Create README files for each folder."""
        logger.info("Creating README files...")
        
        readme_content = {
            'legacy_format': """# Legacy Format Input Sheets

This folder contains legacy format input sheets for OI strategy testing.

Files:
- input_maxoi.xlsx: Legacy OI strategy configuration
- bt_setting.xlsx: Legacy backtester settings

Usage:
These files maintain 100% backward compatibility with the original OI system.
""",
            'enhanced_format': """# Enhanced Format Input Sheets

This folder contains enhanced format input sheets with dynamic weightage support.

Files:
- input_enhanced_oi_config.xlsx: Enhanced OI configuration with 45+ parameters
- bt_setting_enhanced.xlsx: Dynamic weightage configuration

Features:
- Dynamic weight adjustments based on performance
- Multi-factor OI analysis
- Real-time optimization
- Advanced market condition adaptations
""",
            'hybrid_format': """# Hybrid Format Input Sheets

This folder contains hybrid format input sheets combining legacy and enhanced features.

Files:
- input_oi_portfolio.xlsx: Portfolio configuration with selective enhancements

Usage:
Hybrid mode allows gradual migration from legacy to enhanced features.
""",
            'test_outputs': """# Test Output Files

This folder stores output files from E2E testing.

Structure:
- Legacy test outputs
- Enhanced test outputs  
- Hybrid test outputs
- Performance comparison reports
- Golden format validation results
""",
            'documentation': """# Documentation Files

This folder contains documentation and utility files.

Files:
- column_mapping_ml_oi.md: Complete column mapping documentation
- create_enhanced_oi_templates.py: Template generation utility
"""
        }
        
        try:
            for folder_name, content in readme_content.items():
                readme_file = self.oi_path / folder_name / 'README.md'
                with open(readme_file, 'w') as f:
                    f.write(content)
                logger.info(f"✓ Created README for {folder_name}")
                
        except Exception as e:
            logger.error(f"Failed to create README files: {e}")
    
    def run_full_organization(self):
        """Run complete organization process."""
        logger.info("Starting full input sheet organization...")
        
        success = True
        
        # Step 1: Backup old files
        if not self.backup_old_files():
            logger.error("Backup failed")
            success = False
        
        # Step 2: Validate current sheets
        validation_results = self.validate_input_sheets()
        if not validation_results:
            logger.error("Validation failed")
            success = False
        
        # Step 3: Create clean structure
        if not self.create_clean_structure():
            logger.error("Clean structure creation failed")
            success = False
        
        if success:
            logger.info("\n=== ORGANIZATION COMPLETED SUCCESSFULLY ===")
            logger.info("Input sheets are now organized and ready for testing")
        else:
            logger.error("\n=== ORGANIZATION FAILED ===")
            logger.error("Some steps failed - check logs for details")
        
        return success


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description='Input Sheet Organization Tool')
    parser.add_argument('--backup-old', action='store_true',
                       help='Backup old input files')
    parser.add_argument('--validate-sheets', action='store_true',
                       help='Validate input sheet integrity')
    parser.add_argument('--create-clean-structure', action='store_true',
                       help='Create clean folder structure')
    parser.add_argument('--full-organization', action='store_true',
                       help='Run complete organization process')
    
    args = parser.parse_args()
    
    # Initialize organizer
    organizer = InputSheetOrganizer()
    
    if args.backup_old:
        success = organizer.backup_old_files()
    elif args.validate_sheets:
        success = organizer.validate_input_sheets() is not None
    elif args.create_clean_structure:
        success = organizer.create_clean_structure()
    elif args.full_organization:
        success = organizer.run_full_organization()
    else:
        # Default: run full organization
        success = organizer.run_full_organization()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
