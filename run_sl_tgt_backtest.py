#!/usr/bin/env python3
"""
Run SL/TGT test scenarios through GPU backtest
"""

import os
import subprocess
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_sl_tgt_backtest(scenario_name):
    """Run a specific SL/TGT scenario through GPU backtest."""
    
    test_dir = '/srv/samba/shared/test_results/comprehensive_sl_tgt_test'
    portfolio_file = os.path.join(test_dir, f"{scenario_name}_portfolio.xlsx")
    output_path = os.path.join(test_dir, f"{scenario_name}_gpu_output.xlsx")
    
    if not os.path.exists(portfolio_file):
        logger.error(f"Portfolio file not found: {portfolio_file}")
        return None
    
    logger.info(f"\n=== Running {scenario_name} through GPU Backtest ===")
    
    # Set environment for golden format
    env = os.environ.copy()
    env['USE_GOLDEN_FORMAT'] = 'true'
    
    # Command to run GPU backtest
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_path,
        '--debug'
    ]
    
    logger.info(f"Command: {' '.join(cmd)}")
    
    # Run the backtest
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode == 0:
        logger.info("✅ Backtest completed successfully")
        
        # Analyze the output
        if os.path.exists(output_path):
            analyze_backtest_output(output_path, scenario_name)
            return output_path
        else:
            logger.error("Output file not created")
    else:
        logger.error(f"❌ Backtest failed: {result.stderr}")
        # Log the full error for debugging
        if result.stderr:
            logger.error("Full error output:")
            logger.error(result.stderr)
    
    return None


def analyze_backtest_output(output_path, scenario_name):
    """Analyze the backtest output for SL/TGT hits."""
    
    logger.info(f"\n=== Analyzing {scenario_name} Output ===")
    
    try:
        xl = pd.ExcelFile(output_path)
        
        # Check sheets
        logger.info(f"Sheets: {xl.sheet_names}")
        
        # Analyze PORTFOLIO Trans
        if 'PORTFOLIO Trans' in xl.sheet_names:
            df = pd.read_excel(xl, 'PORTFOLIO Trans')
            logger.info(f"Total trades: {len(df)}")
            
            # Check exit reasons
            if 'Reason' in df.columns:
                reason_counts = df['Reason'].value_counts()
                logger.info("\nExit Reasons:")
                for reason, count in reason_counts.items():
                    logger.info(f"  {reason}: {count}")
                
                # Calculate SL/TGT statistics
                sl_hits = len(df[df['Reason'].str.contains('SL', na=False)])
                tgt_hits = len(df[df['Reason'].str.contains('TGT|Target', na=False, case=False)])
                time_exits = len(df[df['Reason'].str.contains('Time', na=False, case=False)])
                
                total_trades = len(df)
                logger.info(f"\nSummary:")
                logger.info(f"  SL Hits: {sl_hits} ({sl_hits/total_trades*100:.1f}%)")
                logger.info(f"  TGT Hits: {tgt_hits} ({tgt_hits/total_trades*100:.1f}%)")
                logger.info(f"  Time Exits: {time_exits} ({time_exits/total_trades*100:.1f}%)")
                
                # Check P&L distribution
                if 'Net PNL' in df.columns:
                    logger.info(f"\nP&L Summary:")
                    logger.info(f"  Total P&L: {df['Net PNL'].sum():.2f}")
                    logger.info(f"  Win Rate: {(df['Net PNL'] > 0).sum() / len(df) * 100:.1f}%")
                    logger.info(f"  Max Profit: {df['Net PNL'].max():.2f}")
                    logger.info(f"  Max Loss: {df['Net PNL'].min():.2f}")
            else:
                logger.warning("No 'Reason' column found in output")
                
        # Check Metrics sheet
        if 'Metrics' in xl.sheet_names:
            metrics_df = pd.read_excel(xl, 'Metrics')
            logger.info("\nMetrics:")
            for _, row in metrics_df.iterrows():
                if 'Particulars' in metrics_df.columns:
                    logger.info(f"  {row['Particulars']}: {row.get('Combined', 'N/A')}")
                    
    except Exception as e:
        logger.error(f"Error analyzing output: {e}")
        import traceback
        traceback.print_exc()


def run_all_scenarios():
    """Run all test scenarios."""
    
    scenarios = [
        'ATM_TIGHT_SL',      # Tight 10% SL - should trigger SL hits
        'OTM_TIGHT_TGT',     # Tight 15% target - should trigger TGT hits
        'ITM_POINTS_SL',     # Points-based SL/TGT
        'PREMIUM_BASED',     # Premium matching strategy
        'IRON_CONDOR'        # Multi-leg strategy
    ]
    
    results = []
    
    for scenario in scenarios:
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing Scenario: {scenario}")
        logger.info(f"{'='*60}")
        
        output_path = run_sl_tgt_backtest(scenario)
        if output_path:
            results.append({
                'scenario': scenario,
                'output': output_path,
                'status': 'SUCCESS'
            })
        else:
            results.append({
                'scenario': scenario,
                'output': None,
                'status': 'FAILED'
            })
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("Test Summary")
    logger.info("="*60)
    
    for result in results:
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        logger.info(f"{status_icon} {result['scenario']}: {result['status']}")
    
    return results


if __name__ == "__main__":
    # First, let's check if we can run a simple test
    logger.info("Starting SL/TGT Backtest Tests...")
    
    # Run the ATM_TIGHT_SL scenario first as a test
    test_scenario = 'ATM_TIGHT_SL'
    output = run_sl_tgt_backtest(test_scenario)
    
    if output:
        logger.info(f"\n✅ Test scenario '{test_scenario}' completed successfully!")
        logger.info(f"Output file: {output}")
    else:
        logger.info(f"\n❌ Test scenario '{test_scenario}' failed")
        logger.info("\nChecking if input files are properly formatted...")
        
        # Debug: Show the structure of the input files
        test_dir = '/srv/samba/shared/test_results/comprehensive_sl_tgt_test'
        portfolio_file = os.path.join(test_dir, f"{test_scenario}_portfolio.xlsx")
        
        if os.path.exists(portfolio_file):
            xl = pd.ExcelFile(portfolio_file)
            logger.info(f"\nPortfolio file sheets: {xl.sheet_names}")
            
            # Show portfolio settings
            df = pd.read_excel(xl, 'PortfolioSetting')
            logger.info("\nPortfolioSetting:")
            logger.info(df.to_string())