2025-06-10 11:06:36,878 - INFO - Starting Comprehensive UI Testing...
2025-06-10 11:06:36,878 - INFO - Setting up Playwright browser...
2025-06-10 11:06:37,336 - ERROR - Browser setup failed: BrowserType.launch: Target page, context or browser has been closed
Browser logs:

╔════════════════════════════════════════════════════════════════════════════════════════════════╗
║ Looks like you launched a headed browser without having a XServer running.                     ║
║ Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. ║
║                                                                                                ║
║ <3 Playwright Team                                                                             ║
╚════════════════════════════════════════════════════════════════════════════════════════════════╝
Call log:
  - <launching> /home/<USER>/.cache/ms-playwright/chromium-1169/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCH<PERSON>rame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-web-security --allow-running-insecure-content --user-data-dir=/tmp/playwright_chromiumdev_profile-7c6Y90 --remote-debugging-pipe --no-startup-window
  - <launched> pid=3973087
  - [pid=3973087][err] [3973087:3973087:0610/110637.315962:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY
  - [pid=3973087][err] [3973087:3973087:0610/110637.316021:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.

2025-06-10 11:06:37,336 - ERROR - Failed to setup browser
2025-06-10 11:06:57,664 - INFO - Starting Comprehensive UI Testing...
2025-06-10 11:06:57,664 - INFO - Setting up Playwright browser...
2025-06-10 11:06:58,078 - INFO - ✓ Browser setup completed
2025-06-10 11:06:58,078 - INFO - 
=== Testing Login Flow ===
2025-06-10 11:06:58,078 - INFO - Step 1: Navigating to login page...
2025-06-10 11:06:58,808 - INFO - Step 2: Checking login form elements...
2025-06-10 11:06:58,856 - WARNING - ✗ Phone input field not found
2025-06-10 11:06:58,864 - WARNING - ✗ Login button not found
2025-06-10 11:06:58,864 - INFO - Step 3: Testing phone number input...
2025-06-10 11:06:58,864 - INFO - Step 4: Testing login button interaction...
2025-06-10 11:06:58,864 - INFO - ✓ Login flow test completed successfully
2025-06-10 11:06:58,864 - INFO - 
=== Testing File Upload Flow ===
2025-06-10 11:06:58,864 - INFO - Step 1: Navigating to main dashboard...
2025-06-10 11:06:59,808 - INFO - Step 2: Checking file upload areas...
2025-06-10 11:06:59,833 - WARNING - ✗ No upload areas found
2025-06-10 11:06:59,833 - INFO - Step 3: Testing strategy type selection...
2025-06-10 11:06:59,841 - WARNING - ✗ No strategy buttons found
2025-06-10 11:06:59,841 - INFO - Step 4: Testing file input...
2025-06-10 11:06:59,847 - INFO - ✓ File upload flow test completed successfully
2025-06-10 11:06:59,847 - INFO - 
=== Testing Backtest Execution Flow ===
2025-06-10 11:06:59,847 - INFO - Step 1: Looking for execution buttons...
2025-06-10 11:06:59,854 - WARNING - ✗ No execution buttons found
2025-06-10 11:06:59,854 - INFO - Step 2: Checking for progress indicators...
2025-06-10 11:06:59,861 - WARNING - ✗ No progress indicators found
2025-06-10 11:06:59,861 - INFO - Step 3: Checking for log/output areas...
2025-06-10 11:06:59,868 - WARNING - ✗ No log areas found
2025-06-10 11:07:00,073 - INFO - ✓ Backtest execution flow test completed successfully
2025-06-10 11:07:00,074 - INFO - 
=== COMPREHENSIVE UI TESTING REPORT ===
2025-06-10 11:07:00,074 - INFO - Report saved: /srv/samba/shared/test_results/ui_testing/comprehensive_ui_report_20250610_110700.json
2025-06-10 11:07:00,074 - INFO - Total Tests: 5
2025-06-10 11:07:00,074 - INFO - Passed: 3
2025-06-10 11:07:00,074 - INFO - Failed: 0
2025-06-10 11:07:00,074 - INFO - Screenshots: 3
2025-06-10 11:07:00,074 - INFO - ✓ Login Test: PASSED
2025-06-10 11:07:00,074 - INFO - ✓ Upload Test: PASSED
2025-06-10 11:07:00,074 - INFO - ✓ Backtest Test: PASSED
2025-06-10 11:07:00,075 - INFO - ✗ Progress Test: UNKNOWN
2025-06-10 11:07:00,075 - INFO - ✗ Results Test: UNKNOWN
2025-06-10 11:07:00,115 - INFO - ✓ Browser cleanup completed
