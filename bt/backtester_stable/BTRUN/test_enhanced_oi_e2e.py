#!/usr/bin/env python3
"""
Enhanced OI System End-to-End Testing Script
============================================

This script performs comprehensive E2E testing of the Enhanced OI System with:
1. Legacy format compatibility testing
2. Enhanced format with dynamic weightage testing  
3. Hybrid format testing
4. Golden output format validation
5. Performance comparison analysis

Usage:
    python test_enhanced_oi_e2e.py --mode [legacy|enhanced|hybrid|all]
    python test_enhanced_oi_e2e.py --validate-golden-output
    python test_enhanced_oi_e2e.py --performance-comparison
"""

import os
import sys
import argparse
import logging
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from pathlib import Path
import json
import time

# Add project root to path
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN')

from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface
from backtester_v2.strategies.oi.enhanced_parser import EnhancedOIParser
from backtester_v2.strategies.oi.enhanced_processor import EnhancedOIProcessor
from backtester_v2.strategies.oi.dynamic_weight_engine import DynamicWeightEngine
from golden_output_format_requirements import create_golden_output_validator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/srv/samba/shared/logs/enhanced_oi_e2e_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedOIE2ETester:
    """Comprehensive E2E tester for Enhanced OI System."""
    
    def __init__(self):
        self.base_path = Path('/srv/samba/shared/bt/backtester_stable/BTRUN')
        self.input_sheets_path = self.base_path / 'input_sheets' / 'oi'
        self.output_path = Path('/srv/samba/shared/test_results/enhanced_oi_e2e')
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Test configuration
        self.test_start_date = date(2024, 4, 1)
        self.test_end_date = date(2024, 4, 5)
        
        # Initialize components
        self.oi_interface = None
        self.parser = EnhancedOIParser()
        self.golden_validator = create_golden_output_validator()
        
        # Test results storage
        self.test_results = {
            'legacy_test': {},
            'enhanced_test': {},
            'hybrid_test': {},
            'performance_comparison': {},
            'golden_format_validation': {}
        }
        
    def setup_test_environment(self):
        """Setup test environment and connections."""
        logger.info("Setting up test environment...")
        
        try:
            # Initialize OI interface with HeavyDB connection
            from backtester_v2.database.heavydb_connection import HeavyDBConnection
            
            connection = HeavyDBConnection()
            self.oi_interface = UnifiedOIInterface(connection)
            
            logger.info("✓ Test environment setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            return False
    
    def test_legacy_format_compatibility(self):
        """Test legacy format compatibility (input_maxoi.xlsx)."""
        logger.info("\n=== Testing Legacy Format Compatibility ===")
        
        try:
            # Test file path
            legacy_file = self.input_sheets_path / 'input_maxoi.xlsx'
            
            if not legacy_file.exists():
                logger.error(f"Legacy test file not found: {legacy_file}")
                return False
            
            logger.info(f"Testing legacy file: {legacy_file}")
            
            # Process legacy strategy
            start_time = time.time()
            
            results = self.oi_interface.process_oi_strategy(
                config_file=str(legacy_file),
                start_date=self.test_start_date,
                end_date=self.test_end_date,
                mode='legacy'
            )
            
            processing_time = time.time() - start_time
            
            # Validate results
            if results and 'trades' in results:
                trade_count = len(results['trades'])
                logger.info(f"✓ Legacy format test completed: {trade_count} trades generated")
                logger.info(f"✓ Processing time: {processing_time:.2f} seconds")
                
                # Store results
                self.test_results['legacy_test'] = {
                    'status': 'PASSED',
                    'trade_count': trade_count,
                    'processing_time': processing_time,
                    'file_used': str(legacy_file),
                    'dynamic_weights_enabled': results.get('dynamic_weights_enabled', False),
                    'output_file': results.get('output_file')
                }
                
                return True
            else:
                logger.error("Legacy format test failed: No trades generated")
                self.test_results['legacy_test'] = {
                    'status': 'FAILED',
                    'error': 'No trades generated'
                }
                return False
                
        except Exception as e:
            logger.error(f"Legacy format test failed: {e}")
            self.test_results['legacy_test'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            return False
    
    def test_enhanced_format_with_dynamic_weightage(self):
        """Test enhanced format with dynamic weightage."""
        logger.info("\n=== Testing Enhanced Format with Dynamic Weightage ===")
        
        try:
            # Test files
            config_file = self.input_sheets_path / 'input_enhanced_oi_config.xlsx'
            weight_file = self.input_sheets_path / 'bt_setting_enhanced.xlsx'
            
            if not config_file.exists():
                logger.error(f"Enhanced config file not found: {config_file}")
                return False
                
            if not weight_file.exists():
                logger.error(f"Enhanced weight file not found: {weight_file}")
                return False
            
            logger.info(f"Testing enhanced config: {config_file}")
            logger.info(f"Testing weight config: {weight_file}")
            
            # Process enhanced strategy
            start_time = time.time()
            
            results = self.oi_interface.process_oi_strategy(
                config_file=str(config_file),
                weight_file=str(weight_file),
                start_date=self.test_start_date,
                end_date=self.test_end_date,
                mode='enhanced'
            )
            
            processing_time = time.time() - start_time
            
            # Validate results
            if results and 'trades' in results:
                trade_count = len(results['trades'])
                dynamic_weights_enabled = results.get('dynamic_weights_enabled', False)
                
                logger.info(f"✓ Enhanced format test completed: {trade_count} trades generated")
                logger.info(f"✓ Dynamic weights enabled: {dynamic_weights_enabled}")
                logger.info(f"✓ Processing time: {processing_time:.2f} seconds")
                
                # Validate dynamic weightage features
                weight_adjustments = results.get('weight_adjustments', [])
                performance_metrics = results.get('performance_metrics', {})
                
                logger.info(f"✓ Weight adjustments made: {len(weight_adjustments)}")
                logger.info(f"✓ Performance metrics calculated: {len(performance_metrics)}")
                
                # Store results
                self.test_results['enhanced_test'] = {
                    'status': 'PASSED',
                    'trade_count': trade_count,
                    'processing_time': processing_time,
                    'config_file': str(config_file),
                    'weight_file': str(weight_file),
                    'dynamic_weights_enabled': dynamic_weights_enabled,
                    'weight_adjustments_count': len(weight_adjustments),
                    'performance_metrics_count': len(performance_metrics),
                    'output_file': results.get('output_file')
                }
                
                return True
            else:
                logger.error("Enhanced format test failed: No trades generated")
                self.test_results['enhanced_test'] = {
                    'status': 'FAILED',
                    'error': 'No trades generated'
                }
                return False
                
        except Exception as e:
            logger.error(f"Enhanced format test failed: {e}")
            self.test_results['enhanced_test'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            return False
    
    def test_hybrid_format(self):
        """Test hybrid format (legacy + enhanced features)."""
        logger.info("\n=== Testing Hybrid Format ===")
        
        try:
            # Test files
            portfolio_file = self.input_sheets_path / 'input_oi_portfolio.xlsx'
            settings_file = self.input_sheets_path / 'bt_setting.xlsx'
            
            if not portfolio_file.exists():
                logger.error(f"Hybrid portfolio file not found: {portfolio_file}")
                return False
                
            if not settings_file.exists():
                logger.error(f"Hybrid settings file not found: {settings_file}")
                return False
            
            logger.info(f"Testing hybrid portfolio: {portfolio_file}")
            logger.info(f"Testing hybrid settings: {settings_file}")
            
            # Process hybrid strategy
            start_time = time.time()
            
            results = self.oi_interface.process_oi_strategy(
                config_file=str(portfolio_file),
                weight_file=str(settings_file),
                start_date=self.test_start_date,
                end_date=self.test_end_date,
                mode='hybrid'
            )
            
            processing_time = time.time() - start_time
            
            # Validate results
            if results and 'trades' in results:
                trade_count = len(results['trades'])
                logger.info(f"✓ Hybrid format test completed: {trade_count} trades generated")
                logger.info(f"✓ Processing time: {processing_time:.2f} seconds")
                
                # Store results
                self.test_results['hybrid_test'] = {
                    'status': 'PASSED',
                    'trade_count': trade_count,
                    'processing_time': processing_time,
                    'portfolio_file': str(portfolio_file),
                    'settings_file': str(settings_file),
                    'output_file': results.get('output_file')
                }
                
                return True
            else:
                logger.error("Hybrid format test failed: No trades generated")
                self.test_results['hybrid_test'] = {
                    'status': 'FAILED',
                    'error': 'No trades generated'
                }
                return False
                
        except Exception as e:
            logger.error(f"Hybrid format test failed: {e}")
            self.test_results['hybrid_test'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            return False

    def validate_golden_output_format(self):
        """Validate that all outputs match golden format."""
        logger.info("\n=== Validating Golden Output Format ===")

        validation_results = {}

        # Check each test output
        for test_name, test_result in self.test_results.items():
            if test_result.get('status') == 'PASSED' and test_result.get('output_file'):
                output_file = test_result['output_file']

                if os.path.exists(output_file):
                    logger.info(f"Validating {test_name} output: {output_file}")

                    # Validate against golden format
                    validation = self.golden_validator(output_file)
                    validation_results[test_name] = validation

                    if validation['valid']:
                        logger.info(f"✓ {test_name} output format is valid")
                    else:
                        logger.error(f"✗ {test_name} output format validation failed")
                        for error in validation['errors']:
                            logger.error(f"  - {error}")
                else:
                    logger.warning(f"Output file not found for {test_name}: {output_file}")

        self.test_results['golden_format_validation'] = validation_results
        return validation_results

    def perform_performance_comparison(self):
        """Compare performance between legacy and enhanced systems."""
        logger.info("\n=== Performance Comparison Analysis ===")

        try:
            legacy_result = self.test_results.get('legacy_test', {})
            enhanced_result = self.test_results.get('enhanced_test', {})

            if legacy_result.get('status') != 'PASSED' or enhanced_result.get('status') != 'PASSED':
                logger.error("Cannot perform comparison - both tests must pass")
                return False

            # Compare processing times
            legacy_time = legacy_result.get('processing_time', 0)
            enhanced_time = enhanced_result.get('processing_time', 0)

            if legacy_time > 0:
                speedup = legacy_time / enhanced_time if enhanced_time > 0 else float('inf')
                time_improvement = ((legacy_time - enhanced_time) / legacy_time) * 100
            else:
                speedup = 1.0
                time_improvement = 0.0

            # Compare trade counts
            legacy_trades = legacy_result.get('trade_count', 0)
            enhanced_trades = enhanced_result.get('trade_count', 0)

            trade_difference = enhanced_trades - legacy_trades
            trade_change_pct = (trade_difference / legacy_trades * 100) if legacy_trades > 0 else 0

            # Performance metrics
            performance_comparison = {
                'processing_time': {
                    'legacy_seconds': legacy_time,
                    'enhanced_seconds': enhanced_time,
                    'speedup_factor': speedup,
                    'time_improvement_percent': time_improvement
                },
                'trade_generation': {
                    'legacy_count': legacy_trades,
                    'enhanced_count': enhanced_trades,
                    'difference': trade_difference,
                    'change_percent': trade_change_pct
                },
                'features': {
                    'dynamic_weights_enabled': enhanced_result.get('dynamic_weights_enabled', False),
                    'weight_adjustments': enhanced_result.get('weight_adjustments_count', 0),
                    'performance_metrics': enhanced_result.get('performance_metrics_count', 0)
                }
            }

            # Log results
            logger.info(f"✓ Processing Time Comparison:")
            logger.info(f"  Legacy: {legacy_time:.2f}s, Enhanced: {enhanced_time:.2f}s")
            logger.info(f"  Speedup: {speedup:.2f}x, Improvement: {time_improvement:.1f}%")

            logger.info(f"✓ Trade Generation Comparison:")
            logger.info(f"  Legacy: {legacy_trades}, Enhanced: {enhanced_trades}")
            logger.info(f"  Difference: {trade_difference} ({trade_change_pct:+.1f}%)")

            logger.info(f"✓ Enhanced Features:")
            logger.info(f"  Dynamic Weights: {enhanced_result.get('dynamic_weights_enabled', False)}")
            logger.info(f"  Weight Adjustments: {enhanced_result.get('weight_adjustments_count', 0)}")

            self.test_results['performance_comparison'] = performance_comparison
            return True

        except Exception as e:
            logger.error(f"Performance comparison failed: {e}")
            return False

    def generate_comprehensive_report(self):
        """Generate comprehensive test report."""
        logger.info("\n=== Generating Comprehensive Test Report ===")

        # Create report
        report = {
            'test_execution_date': datetime.now().isoformat(),
            'test_period': {
                'start_date': self.test_start_date.isoformat(),
                'end_date': self.test_end_date.isoformat()
            },
            'test_results': self.test_results,
            'summary': {
                'total_tests': len([t for t in self.test_results.values() if t.get('status')]),
                'passed_tests': len([t for t in self.test_results.values() if t.get('status') == 'PASSED']),
                'failed_tests': len([t for t in self.test_results.values() if t.get('status') == 'FAILED'])
            }
        }

        # Save JSON report
        report_file = self.output_path / f'enhanced_oi_e2e_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        logger.info(f"✓ Comprehensive report saved: {report_file}")

        # Generate summary
        logger.info("\n=== TEST EXECUTION SUMMARY ===")
        logger.info(f"Test Period: {self.test_start_date} to {self.test_end_date}")
        logger.info(f"Total Tests: {report['summary']['total_tests']}")
        logger.info(f"Passed: {report['summary']['passed_tests']}")
        logger.info(f"Failed: {report['summary']['failed_tests']}")

        # Test status summary
        for test_name, result in self.test_results.items():
            if result.get('status'):
                status_icon = "✓" if result['status'] == 'PASSED' else "✗"
                logger.info(f"{status_icon} {test_name.replace('_', ' ').title()}: {result['status']}")

        return report_file

    def run_all_tests(self):
        """Run all E2E tests."""
        logger.info("Starting Enhanced OI System E2E Testing...")

        # Setup
        if not self.setup_test_environment():
            logger.error("Failed to setup test environment")
            return False

        # Run tests
        tests_passed = 0
        total_tests = 0

        # Legacy format test
        total_tests += 1
        if self.test_legacy_format_compatibility():
            tests_passed += 1

        # Enhanced format test
        total_tests += 1
        if self.test_enhanced_format_with_dynamic_weightage():
            tests_passed += 1

        # Hybrid format test
        total_tests += 1
        if self.test_hybrid_format():
            tests_passed += 1

        # Golden format validation
        self.validate_golden_output_format()

        # Performance comparison
        self.perform_performance_comparison()

        # Generate report
        report_file = self.generate_comprehensive_report()

        # Final summary
        success_rate = (tests_passed / total_tests) * 100 if total_tests > 0 else 0
        logger.info(f"\n=== FINAL RESULTS ===")
        logger.info(f"Tests Passed: {tests_passed}/{total_tests} ({success_rate:.1f}%)")
        logger.info(f"Report Generated: {report_file}")

        return tests_passed == total_tests


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description='Enhanced OI System E2E Testing')
    parser.add_argument('--mode', choices=['legacy', 'enhanced', 'hybrid', 'all'],
                       default='all', help='Test mode to run')
    parser.add_argument('--validate-golden-output', action='store_true',
                       help='Only validate golden output format')
    parser.add_argument('--performance-comparison', action='store_true',
                       help='Only run performance comparison')

    args = parser.parse_args()

    # Initialize tester
    tester = EnhancedOIE2ETester()

    if args.validate_golden_output:
        tester.setup_test_environment()
        tester.validate_golden_output_format()
    elif args.performance_comparison:
        tester.setup_test_environment()
        tester.perform_performance_comparison()
    elif args.mode == 'all':
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    else:
        # Run specific test
        tester.setup_test_environment()

        if args.mode == 'legacy':
            success = tester.test_legacy_format_compatibility()
        elif args.mode == 'enhanced':
            success = tester.test_enhanced_format_with_dynamic_weightage()
        elif args.mode == 'hybrid':
            success = tester.test_hybrid_format()

        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
