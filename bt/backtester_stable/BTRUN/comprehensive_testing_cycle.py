#!/usr/bin/env python3
"""
Comprehensive Testing Cycle for Enhanced OI System
=================================================

This script implements the complete testing cycle:
1. Run tests
2. Validate results
3. Analyze issues
4. Fix issues
5. Re-test
6. Repeat until desired output achieved

Usage:
    python comprehensive_testing_cycle.py --mode all
    python comprehensive_testing_cycle.py --mode legacy
    python comprehensive_testing_cycle.py --mode enhanced
"""

import os
import sys
import logging
import json
import time
from pathlib import Path
from datetime import datetime, date
import subprocess
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/srv/samba/shared/logs/comprehensive_testing_cycle.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveTestingCycle:
    """Implements comprehensive testing cycle with validation and analysis."""
    
    def __init__(self):
        self.base_path = Path('/srv/samba/shared/bt/backtester_stable/BTRUN')
        self.results_path = Path('/srv/samba/shared/test_results/comprehensive_cycle')
        self.results_path.mkdir(parents=True, exist_ok=True)
        
        # Test configuration
        self.test_configs = {
            'legacy': {
                'command': ['python3', 'BT_OI_GPU.py', '--symbol', 'NIFTY', '--oi-method', 'maxoi_1', 
                           '--timeframe', '3', '--start-date', '240401', '--end-date', '240402'],
                'expected_trades_min': 100,
                'expected_trades_max': 500,
                'description': 'Legacy OI system compatibility test'
            },
            'enhanced': {
                'command': ['python3', 'test_enhanced_oi_e2e.py', '--mode', 'enhanced'],
                'expected_trades_min': 100,
                'expected_trades_max': 500,
                'description': 'Enhanced OI system with dynamic weightage test'
            },
            'hybrid': {
                'command': ['python3', 'test_enhanced_oi_e2e.py', '--mode', 'hybrid'],
                'expected_trades_min': 50,
                'expected_trades_max': 300,
                'description': 'Hybrid OI system test'
            }
        }
        
        # Cycle tracking
        self.cycle_results = {}
        self.max_cycles = 5
        self.current_cycle = 0
        
    def run_test_cycle(self, test_mode):
        """Run a complete test cycle for a specific mode."""
        logger.info(f"\n=== Starting Test Cycle for {test_mode.upper()} ===")
        
        cycle_data = {
            'test_mode': test_mode,
            'cycles': [],
            'final_status': 'PENDING',
            'total_cycles': 0
        }
        
        for cycle_num in range(1, self.max_cycles + 1):
            logger.info(f"\n--- Cycle {cycle_num} for {test_mode} ---")
            
            cycle_result = self.execute_single_cycle(test_mode, cycle_num)
            cycle_data['cycles'].append(cycle_result)
            cycle_data['total_cycles'] = cycle_num
            
            if cycle_result['status'] == 'PASSED':
                logger.info(f"✓ {test_mode} test PASSED in cycle {cycle_num}")
                cycle_data['final_status'] = 'PASSED'
                break
            elif cycle_result['status'] == 'FAILED_CRITICAL':
                logger.error(f"✗ {test_mode} test FAILED CRITICALLY in cycle {cycle_num}")
                cycle_data['final_status'] = 'FAILED_CRITICAL'
                break
            else:
                logger.warning(f"⚠ {test_mode} test needs retry after cycle {cycle_num}")
                if cycle_num < self.max_cycles:
                    self.apply_fixes(cycle_result['issues'])
                    time.sleep(2)  # Brief pause between cycles
        
        if cycle_data['final_status'] == 'PENDING':
            cycle_data['final_status'] = 'FAILED_MAX_CYCLES'
            logger.error(f"✗ {test_mode} test failed after {self.max_cycles} cycles")
        
        self.cycle_results[test_mode] = cycle_data
        return cycle_data
    
    def execute_single_cycle(self, test_mode, cycle_num):
        """Execute a single test cycle."""
        cycle_start = time.time()
        
        cycle_result = {
            'cycle_number': cycle_num,
            'start_time': datetime.now().isoformat(),
            'test_mode': test_mode,
            'status': 'PENDING',
            'execution_time': 0,
            'trade_count': 0,
            'issues': [],
            'validation_results': {},
            'output_files': []
        }
        
        try:
            # Step 1: Execute Test
            logger.info(f"Step 1: Executing {test_mode} test...")
            execution_result = self.execute_test(test_mode)
            
            cycle_result['execution_time'] = execution_result.get('execution_time', 0)
            cycle_result['trade_count'] = execution_result.get('trade_count', 0)
            cycle_result['output_files'] = execution_result.get('output_files', [])
            
            if not execution_result['success']:
                cycle_result['status'] = 'FAILED_EXECUTION'
                cycle_result['issues'].append({
                    'type': 'EXECUTION_FAILURE',
                    'description': execution_result.get('error', 'Unknown execution error'),
                    'severity': 'CRITICAL'
                })
                return cycle_result
            
            # Step 2: Validate Results
            logger.info(f"Step 2: Validating {test_mode} results...")
            validation_result = self.validate_results(test_mode, execution_result)
            cycle_result['validation_results'] = validation_result
            
            # Step 3: Analyze Issues
            logger.info(f"Step 3: Analyzing {test_mode} issues...")
            issues = self.analyze_issues(test_mode, execution_result, validation_result)
            cycle_result['issues'].extend(issues)
            
            # Step 4: Determine Status
            critical_issues = [i for i in cycle_result['issues'] if i['severity'] == 'CRITICAL']
            minor_issues = [i for i in cycle_result['issues'] if i['severity'] in ['MINOR', 'WARNING']]
            
            if critical_issues:
                cycle_result['status'] = 'FAILED_CRITICAL'
            elif minor_issues:
                cycle_result['status'] = 'FAILED_MINOR'
            else:
                cycle_result['status'] = 'PASSED'
            
            cycle_result['execution_time'] = time.time() - cycle_start
            
            # Log cycle summary
            logger.info(f"Cycle {cycle_num} Summary:")
            logger.info(f"  Status: {cycle_result['status']}")
            logger.info(f"  Trade Count: {cycle_result['trade_count']}")
            logger.info(f"  Execution Time: {cycle_result['execution_time']:.2f}s")
            logger.info(f"  Issues Found: {len(cycle_result['issues'])}")
            
            return cycle_result
            
        except Exception as e:
            cycle_result['status'] = 'FAILED_CRITICAL'
            cycle_result['issues'].append({
                'type': 'UNEXPECTED_ERROR',
                'description': str(e),
                'severity': 'CRITICAL'
            })
            cycle_result['execution_time'] = time.time() - cycle_start
            logger.error(f"Unexpected error in cycle {cycle_num}: {e}")
            return cycle_result
    
    def execute_test(self, test_mode):
        """Execute the actual test."""
        config = self.test_configs[test_mode]
        
        try:
            start_time = time.time()
            
            # Execute command
            result = subprocess.run(
                config['command'],
                cwd=self.base_path,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            execution_time = time.time() - start_time
            
            # Parse output for trade count
            trade_count = self.extract_trade_count(result.stdout, result.stderr)
            
            # Check for output files
            output_files = self.find_output_files(test_mode)
            
            return {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'trade_count': trade_count,
                'output_files': output_files,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode,
                'error': result.stderr if result.returncode != 0 else None
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'execution_time': 300,
                'trade_count': 0,
                'output_files': [],
                'error': 'Test execution timed out after 5 minutes'
            }
        except Exception as e:
            return {
                'success': False,
                'execution_time': 0,
                'trade_count': 0,
                'output_files': [],
                'error': str(e)
            }
    
    def extract_trade_count(self, stdout, stderr):
        """Extract trade count from test output."""
        combined_output = stdout + stderr
        
        # Look for trade count patterns
        patterns = [
            r'Total trades: (\d+)',
            r'(\d+) trades',
            r'Completed.*: (\d+) trades',
            r'Test strategy completed: (\d+) trades'
        ]
        
        import re
        for pattern in patterns:
            match = re.search(pattern, combined_output)
            if match:
                return int(match.group(1))
        
        return 0
    
    def find_output_files(self, test_mode):
        """Find output files generated by the test."""
        output_patterns = [
            self.base_path / 'output' / '*.xlsx',
            self.base_path / 'results' / '*.xlsx',
            self.results_path / '*.xlsx',
            self.results_path / '*.json'
        ]
        
        output_files = []
        for pattern in output_patterns:
            output_files.extend(list(pattern.parent.glob(pattern.name)))
        
        return [str(f) for f in output_files if f.exists()]
    
    def validate_results(self, test_mode, execution_result):
        """Validate test results."""
        config = self.test_configs[test_mode]
        validation = {
            'trade_count_valid': False,
            'output_files_exist': False,
            'execution_time_reasonable': False,
            'no_critical_errors': False,
            'details': {}
        }
        
        # Validate trade count
        trade_count = execution_result['trade_count']
        if config['expected_trades_min'] <= trade_count <= config['expected_trades_max']:
            validation['trade_count_valid'] = True
        validation['details']['trade_count'] = {
            'actual': trade_count,
            'expected_min': config['expected_trades_min'],
            'expected_max': config['expected_trades_max']
        }
        
        # Validate output files
        if execution_result['output_files']:
            validation['output_files_exist'] = True
        validation['details']['output_files'] = execution_result['output_files']
        
        # Validate execution time (should be reasonable)
        exec_time = execution_result['execution_time']
        if 1 <= exec_time <= 180:  # Between 1 second and 3 minutes
            validation['execution_time_reasonable'] = True
        validation['details']['execution_time'] = exec_time
        
        # Check for critical errors
        if execution_result['success'] and not any(
            error_term in (execution_result.get('stderr', '') + execution_result.get('stdout', '')).lower()
            for error_term in ['critical', 'fatal', 'exception', 'traceback']
        ):
            validation['no_critical_errors'] = True
        
        return validation
    
    def analyze_issues(self, test_mode, execution_result, validation_result):
        """Analyze issues found during testing."""
        issues = []
        
        # Check validation failures
        if not validation_result['trade_count_valid']:
            issues.append({
                'type': 'TRADE_COUNT_INVALID',
                'description': f"Trade count {execution_result['trade_count']} outside expected range",
                'severity': 'CRITICAL' if execution_result['trade_count'] == 0 else 'MINOR'
            })
        
        if not validation_result['output_files_exist']:
            issues.append({
                'type': 'NO_OUTPUT_FILES',
                'description': 'No output files generated',
                'severity': 'MINOR'
            })
        
        if not validation_result['execution_time_reasonable']:
            issues.append({
                'type': 'EXECUTION_TIME_ISSUE',
                'description': f"Execution time {execution_result['execution_time']:.2f}s is unreasonable",
                'severity': 'WARNING'
            })
        
        if not validation_result['no_critical_errors']:
            issues.append({
                'type': 'CRITICAL_ERRORS_FOUND',
                'description': 'Critical errors detected in output',
                'severity': 'CRITICAL'
            })
        
        return issues
    
    def apply_fixes(self, issues):
        """Apply fixes for identified issues."""
        logger.info("Applying fixes for identified issues...")
        
        for issue in issues:
            if issue['type'] == 'TRADE_COUNT_INVALID':
                logger.info("- Adjusting test parameters for trade count issues")
                # Could adjust date ranges, parameters, etc.
                
            elif issue['type'] == 'NO_OUTPUT_FILES':
                logger.info("- Ensuring output directories exist")
                self.results_path.mkdir(parents=True, exist_ok=True)
                
            elif issue['type'] == 'EXECUTION_TIME_ISSUE':
                logger.info("- Optimizing test execution parameters")
                # Could reduce date ranges for faster execution
                
            elif issue['type'] == 'CRITICAL_ERRORS_FOUND':
                logger.info("- Attempting to resolve critical errors")
                # Could restart services, clear caches, etc.
        
        logger.info("Fixes applied, ready for next cycle")
    
    def generate_comprehensive_report(self):
        """Generate comprehensive test cycle report."""
        report = {
            'test_execution_date': datetime.now().isoformat(),
            'total_test_modes': len(self.cycle_results),
            'cycle_results': self.cycle_results,
            'summary': {
                'passed_tests': 0,
                'failed_tests': 0,
                'critical_failures': 0,
                'total_cycles_executed': 0
            }
        }
        
        # Calculate summary
        for test_mode, data in self.cycle_results.items():
            report['summary']['total_cycles_executed'] += data['total_cycles']
            
            if data['final_status'] == 'PASSED':
                report['summary']['passed_tests'] += 1
            elif data['final_status'] in ['FAILED_CRITICAL', 'FAILED_MAX_CYCLES']:
                report['summary']['failed_tests'] += 1
                if data['final_status'] == 'FAILED_CRITICAL':
                    report['summary']['critical_failures'] += 1
        
        # Save report
        report_file = self.results_path / f'comprehensive_cycle_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"\n=== COMPREHENSIVE TESTING CYCLE REPORT ===")
        logger.info(f"Report saved: {report_file}")
        logger.info(f"Total Test Modes: {report['total_test_modes']}")
        logger.info(f"Passed Tests: {report['summary']['passed_tests']}")
        logger.info(f"Failed Tests: {report['summary']['failed_tests']}")
        logger.info(f"Critical Failures: {report['summary']['critical_failures']}")
        logger.info(f"Total Cycles Executed: {report['summary']['total_cycles_executed']}")
        
        return report_file
    
    def run_all_test_cycles(self):
        """Run comprehensive testing cycles for all modes."""
        logger.info("Starting Comprehensive Testing Cycle for All Modes...")
        
        for test_mode in ['legacy', 'enhanced', 'hybrid']:
            self.run_test_cycle(test_mode)
        
        # Generate final report
        report_file = self.generate_comprehensive_report()
        
        # Determine overall success
        all_passed = all(
            data['final_status'] == 'PASSED' 
            for data in self.cycle_results.values()
        )
        
        if all_passed:
            logger.info("\n🎉 ALL TESTS PASSED! System ready for production.")
        else:
            logger.error("\n❌ Some tests failed. Review issues and retry.")
        
        return all_passed, report_file


def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Comprehensive Testing Cycle')
    parser.add_argument('--mode', choices=['all', 'legacy', 'enhanced', 'hybrid'], 
                       default='all', help='Test mode to run')
    
    args = parser.parse_args()
    
    # Initialize testing cycle
    tester = ComprehensiveTestingCycle()
    
    if args.mode == 'all':
        success, report_file = tester.run_all_test_cycles()
        sys.exit(0 if success else 1)
    else:
        cycle_data = tester.run_test_cycle(args.mode)
        success = cycle_data['final_status'] == 'PASSED'
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
