#!/usr/bin/env python3
"""
Run comprehensive comparison between Archive and GPU systems
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
import logging
from datetime import datetime
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveComparison:
    def __init__(self):
        self.results_dir = '/srv/samba/shared/test_results/comprehensive_comparison'
        os.makedirs(self.results_dir, exist_ok=True)
        self.comparison_results = []
        
    def prepare_test_portfolio(self):
        """Prepare a standardized test portfolio for both systems."""
        
        logger.info("=== Preparing Test Portfolio ===")
        
        # Use the existing TBS input as base
        portfolio_data = {
            'PortfolioSetting': pd.DataFrame([{
                'StartDate': '01_04_2024',
                'EndDate': '05_04_2024',
                'IsTickBT': 'YES',
                'Enabled': 'YES',
                'PortfolioName': 'COMPARISON_TEST',
                'PortfolioTarget': 100000,
                'PortfolioStoploss': 50000,
                'PortfolioTrailingType': 'none',
                'PnLCalTime': 152500,
                'LockPercent': 0,
                'TrailPercent': 0,
                'Multiplier': 1.0,
                'SlippagePercent': 0.05
            }]),
            'StrategySetting': pd.DataFrame([{
                'Enabled': 'YES',
                'PortfolioName': 'COMPARISON_TEST',
                'StrategyType': 'TBS',
                'StrategyExcelFilePath': os.path.join(self.results_dir, 'test_strategy.xlsx')
            }])
        }
        
        # Create portfolio file
        portfolio_file = os.path.join(self.results_dir, 'test_portfolio.xlsx')
        with pd.ExcelWriter(portfolio_file) as writer:
            for sheet, df in portfolio_data.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
                
        # Create strategy with mixed SL/TGT settings
        strategy_data = {
            'GeneralParameter': pd.DataFrame([{
                'StrategyName': 'COMPREHENSIVE_TEST',
                'MoveSlToCost': 'no',
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'Weekdays': '1,2,3,4,5',
                'DTE': 0,
                'StrikeSelectionTime': 91500,
                'StartTime': 91500,
                'LastEntryTime': 93000,
                'EndTime': 152500,
                'StrategyProfit': 10000,
                'StrategyLoss': 5000,
                'StrategyProfitReExecuteNo': 0,
                'StrategyLossReExecuteNo': 0,
                'StrategyTrailingType': 'none',
                'PnLCalTime': 152500,
                'LockPercent': 0,
                'TrailPercent': 0,
                'SqOff1Time': 230000,
                'SqOff1Percent': 0,
                'SqOff2Time': 230000,
                'SqOff2Percent': 0,
                'ProfitReaches': 0,
                'LockMinProfitAt': 0,
                'IncreaseInProfit': 0,
                'TrailMinProfitBy': 0,
                'TgtTrackingFrom': 'high/low',
                'TgtRegisterPriceFrom': 'tracking',
                'SlTrackingFrom': 'high/low',
                'SlRegisterPriceFrom': 'tracking',
                'PnLCalculationFrom': 'close',
                'ConsiderHedgePnLForStgyPnL': 'no',
                'StoplossCheckingInterval': 1,
                'TargetCheckingInterval': 1,
                'ReEntryCheckingInterval': 1,
                'OnExpiryDayTradeNextExpiry': 'no'
            }]),
            'LegParameter': pd.DataFrame([
                {
                    'StrategyName': 'COMPREHENSIVE_TEST',
                    'IsIdle': 'no',
                    'LegID': 1,
                    'Instrument': 'call',
                    'Transaction': 'sell',
                    'Expiry': 'current',
                    'W&Type': 'percentage',
                    'W&TValue': 0,
                    'TrailW&T': 'no',
                    'StrikeMethod': 'atm',
                    'MatchPremium': 'high',
                    'StrikeValue': 0,
                    'StrikePremiumCondition': '=',
                    'SLType': 'percentage',
                    'SLValue': 20,  # 20% SL
                    'TGTType': 'percentage',
                    'TGTValue': 30, # 30% target
                    'TrailSLType': 'percentage',
                    'SL_TrailAt': 0,
                    'SL_TrailBy': 0,
                    'Lots': 2,
                    'ReEntryType': 'none',
                    'ReEnteriesCount': 0,
                    'OnEntry_OpenTradeOn': 0,
                    'OnEntry_SqOffTradeOff': 0,
                    'OnEntry_SqOffAllLegs': 'no',
                    'OnEntry_OpenTradeDelay': 0,
                    'OnEntry_SqOffDelay': 0,
                    'OnExit_OpenTradeOn': 0,
                    'OnExit_SqOffTradeOff': 0,
                    'OnExit_SqOffAllLegs': 'no',
                    'OnExit_OpenAllLegs': 'no',
                    'OnExit_OpenTradeDelay': 0,
                    'OnExit_SqOffDelay': 0,
                    'OpenHedge': 'No',
                    'HedgeStrikeMethod': 'atm',
                    'HedgeStrikeValue': 0,
                    'HedgeStrikePremiumCondition': '='
                },
                {
                    'StrategyName': 'COMPREHENSIVE_TEST',
                    'IsIdle': 'no',
                    'LegID': 2,
                    'Instrument': 'put',
                    'Transaction': 'sell',
                    'Expiry': 'current',
                    'W&Type': 'percentage',
                    'W&TValue': 0,
                    'TrailW&T': 'no',
                    'StrikeMethod': 'atm',
                    'MatchPremium': 'high',
                    'StrikeValue': 0,
                    'StrikePremiumCondition': '=',
                    'SLType': 'percentage',
                    'SLValue': 20,
                    'TGTType': 'percentage',
                    'TGTValue': 30,
                    'TrailSLType': 'percentage',
                    'SL_TrailAt': 0,
                    'SL_TrailBy': 0,
                    'Lots': 2,
                    'ReEntryType': 'none',
                    'ReEnteriesCount': 0,
                    'OnEntry_OpenTradeOn': 0,
                    'OnEntry_SqOffTradeOff': 0,
                    'OnEntry_SqOffAllLegs': 'no',
                    'OnEntry_OpenTradeDelay': 0,
                    'OnEntry_SqOffDelay': 0,
                    'OnExit_OpenTradeOn': 0,
                    'OnExit_SqOffTradeOff': 0,
                    'OnExit_SqOffAllLegs': 'no',
                    'OnExit_OpenAllLegs': 'no',
                    'OnExit_OpenTradeDelay': 0,
                    'OnExit_SqOffDelay': 0,
                    'OpenHedge': 'No',
                    'HedgeStrikeMethod': 'atm',
                    'HedgeStrikeValue': 0,
                    'HedgeStrikePremiumCondition': '='
                }
            ])
        }
        
        strategy_file = os.path.join(self.results_dir, 'test_strategy.xlsx')
        with pd.ExcelWriter(strategy_file) as writer:
            for sheet, df in strategy_data.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
                
        logger.info(f"Created test portfolio: {portfolio_file}")
        logger.info(f"Created test strategy: {strategy_file}")
        
        return portfolio_file, strategy_file
        
    def run_archive_backtest(self, portfolio_file):
        """Run backtest using archive system."""
        
        logger.info("\n=== Running Archive System Backtest ===")
        
        output_path = os.path.join(self.results_dir, 'archive_output.xlsx')
        
        # Command for archive system
        cmd = [
            'python3',
            '/srv/samba/shared/BTRunPortfolio.py',  # Archive system
            '--portfolio-excel', portfolio_file,
            '--output-path', output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ Archive backtest completed successfully")
                return output_path
            else:
                logger.error(f"❌ Archive backtest failed: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            logger.error("Archive backtest timed out")
            return None
        except Exception as e:
            logger.error(f"Error running archive backtest: {e}")
            return None
            
    def run_gpu_backtest(self, portfolio_file):
        """Run backtest using GPU system."""
        
        logger.info("\n=== Running GPU System Backtest ===")
        
        output_path = os.path.join(self.results_dir, 'gpu_output.xlsx')
        
        # Set environment for golden format
        env = os.environ.copy()
        env['USE_GOLDEN_FORMAT'] = 'true'
        
        # Command for GPU system
        cmd = [
            'python3',
            '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
            '--portfolio-excel', portfolio_file,
            '--output-path', output_path,
            '--debug'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ GPU backtest completed successfully")
                return output_path
            else:
                logger.error(f"❌ GPU backtest failed: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            logger.error("GPU backtest timed out")
            return None
        except Exception as e:
            logger.error(f"Error running GPU backtest: {e}")
            return None
            
    def compare_outputs(self, archive_output, gpu_output):
        """Compare outputs from both systems."""
        
        logger.info("\n=== Comparing Outputs ===")
        
        comparison = {
            'timestamp': datetime.now().isoformat(),
            'archive_output': archive_output,
            'gpu_output': gpu_output,
            'sheets_match': False,
            'columns_match': False,
            'trade_count_match': False,
            'exit_reasons_match': False,
            'pnl_match': False,
            'details': {}
        }
        
        try:
            # Load both files
            archive_xl = pd.ExcelFile(archive_output) if archive_output else None
            gpu_xl = pd.ExcelFile(gpu_output) if gpu_output else None
            
            if not archive_xl or not gpu_xl:
                comparison['error'] = 'One or both outputs missing'
                return comparison
                
            # Compare sheets
            archive_sheets = set(archive_xl.sheet_names)
            gpu_sheets = set(gpu_xl.sheet_names)
            
            comparison['details']['archive_sheets'] = list(archive_sheets)
            comparison['details']['gpu_sheets'] = list(gpu_sheets)
            comparison['sheets_match'] = len(archive_sheets & gpu_sheets) >= 7  # Allow some variation
            
            # Compare PORTFOLIO Trans
            if 'PORTFOLIO Trans' in archive_sheets and 'PORTFOLIO Trans' in gpu_sheets:
                archive_trans = pd.read_excel(archive_xl, 'PORTFOLIO Trans')
                gpu_trans = pd.read_excel(gpu_xl, 'PORTFOLIO Trans')
                
                # Column comparison
                comparison['details']['archive_columns'] = len(archive_trans.columns)
                comparison['details']['gpu_columns'] = len(gpu_trans.columns)
                comparison['columns_match'] = len(archive_trans.columns) == len(gpu_trans.columns)
                
                # Trade count
                comparison['details']['archive_trades'] = len(archive_trans)
                comparison['details']['gpu_trades'] = len(gpu_trans)
                comparison['trade_count_match'] = len(archive_trans) == len(gpu_trans)
                
                # Exit reasons
                if 'Reason' in archive_trans.columns and 'Reason' in gpu_trans.columns:
                    archive_reasons = archive_trans['Reason'].value_counts().to_dict()
                    gpu_reasons = gpu_trans['Reason'].value_counts().to_dict()
                    
                    comparison['details']['archive_exit_reasons'] = archive_reasons
                    comparison['details']['gpu_exit_reasons'] = gpu_reasons
                    comparison['exit_reasons_match'] = archive_reasons == gpu_reasons
                    
                # P&L comparison
                if 'Net PNL' in archive_trans.columns and 'Net PNL' in gpu_trans.columns:
                    archive_pnl = archive_trans['Net PNL'].sum()
                    gpu_pnl = gpu_trans['Net PNL'].sum()
                    
                    comparison['details']['archive_total_pnl'] = float(archive_pnl)
                    comparison['details']['gpu_total_pnl'] = float(gpu_pnl)
                    comparison['pnl_match'] = abs(archive_pnl - gpu_pnl) < 0.01
                    
                # Strike selection comparison
                if 'Strike' in archive_trans.columns and 'Strike' in gpu_trans.columns:
                    archive_strikes = sorted(archive_trans['Strike'].unique())
                    gpu_strikes = sorted(gpu_trans['Strike'].unique())
                    
                    comparison['details']['archive_strikes'] = archive_strikes
                    comparison['details']['gpu_strikes'] = gpu_strikes
                    comparison['details']['strikes_match'] = archive_strikes == gpu_strikes
                    
        except Exception as e:
            comparison['error'] = str(e)
            logger.error(f"Error comparing outputs: {e}")
            
        return comparison
        
    def analyze_differences(self, comparison):
        """Analyze and report differences between systems."""
        
        logger.info("\n=== Analysis Report ===")
        
        # Overall match status
        all_match = all([
            comparison.get('sheets_match', False),
            comparison.get('columns_match', False),
            comparison.get('trade_count_match', False),
            comparison.get('exit_reasons_match', False),
            comparison.get('pnl_match', False)
        ])
        
        if all_match:
            logger.info("✅ PERFECT MATCH: Both systems produce identical results!")
        else:
            logger.info("❌ DIFFERENCES FOUND:")
            
            if not comparison.get('sheets_match', False):
                logger.info("  - Sheet structure differs")
                
            if not comparison.get('columns_match', False):
                logger.info("  - Column count differs")
                details = comparison.get('details', {})
                logger.info(f"    Archive: {details.get('archive_columns')} columns")
                logger.info(f"    GPU: {details.get('gpu_columns')} columns")
                
            if not comparison.get('trade_count_match', False):
                logger.info("  - Trade count differs")
                details = comparison.get('details', {})
                logger.info(f"    Archive: {details.get('archive_trades')} trades")
                logger.info(f"    GPU: {details.get('gpu_trades')} trades")
                
            if not comparison.get('exit_reasons_match', False):
                logger.info("  - Exit reasons differ")
                details = comparison.get('details', {})
                logger.info(f"    Archive: {details.get('archive_exit_reasons')}")
                logger.info(f"    GPU: {details.get('gpu_exit_reasons')}")
                
            if not comparison.get('pnl_match', False):
                logger.info("  - P&L differs")
                details = comparison.get('details', {})
                logger.info(f"    Archive P&L: {details.get('archive_total_pnl', 0):.2f}")
                logger.info(f"    GPU P&L: {details.get('gpu_total_pnl', 0):.2f}")
                
        # Save detailed comparison
        comparison_file = os.path.join(self.results_dir, 'comparison_results.json')
        with open(comparison_file, 'w') as f:
            json.dump(comparison, f, indent=2)
        logger.info(f"\nDetailed comparison saved to: {comparison_file}")
        
        return all_match
        
    def run_multiple_scenarios(self):
        """Run multiple test scenarios."""
        
        scenarios = [
            {
                'name': 'Basic ATM Straddle',
                'legs': [
                    {'Instrument': 'call', 'StrikeMethod': 'atm', 'SLValue': 20, 'TGTValue': 30},
                    {'Instrument': 'put', 'StrikeMethod': 'atm', 'SLValue': 20, 'TGTValue': 30}
                ]
            },
            {
                'name': 'OTM Strangle',
                'legs': [
                    {'Instrument': 'call', 'StrikeMethod': 'otm', 'StrikeValue': 2, 'SLValue': 15, 'TGTValue': 25},
                    {'Instrument': 'put', 'StrikeMethod': 'otm', 'StrikeValue': 2, 'SLValue': 15, 'TGTValue': 25}
                ]
            },
            {
                'name': 'ITM Strategy',
                'legs': [
                    {'Instrument': 'call', 'StrikeMethod': 'itm', 'StrikeValue': 1, 'SLValue': 25, 'TGTValue': 40},
                    {'Instrument': 'put', 'StrikeMethod': 'itm', 'StrikeValue': 1, 'SLValue': 25, 'TGTValue': 40}
                ]
            }
        ]
        
        all_results = []
        
        for scenario in scenarios:
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing Scenario: {scenario['name']}")
            logger.info(f"{'='*60}")
            
            # Update strategy file for this scenario
            # ... (implementation for updating strategy based on scenario)
            
            # For now, run with default scenario
            portfolio_file, _ = self.prepare_test_portfolio()
            
            # Run both systems
            archive_output = self.run_archive_backtest(portfolio_file)
            gpu_output = self.run_gpu_backtest(portfolio_file)
            
            # Compare
            comparison = self.compare_outputs(archive_output, gpu_output)
            comparison['scenario'] = scenario['name']
            
            # Analyze
            match_status = self.analyze_differences(comparison)
            
            all_results.append({
                'scenario': scenario['name'],
                'match': match_status,
                'comparison': comparison
            })
            
        return all_results
        
    def run(self):
        """Run the comprehensive comparison."""
        
        logger.info("="*80)
        logger.info("COMPREHENSIVE COMPARISON: Archive vs GPU System")
        logger.info("="*80)
        
        # Prepare test data
        portfolio_file, strategy_file = self.prepare_test_portfolio()
        
        # Run both systems
        archive_output = self.run_archive_backtest(portfolio_file)
        gpu_output = self.run_gpu_backtest(portfolio_file)
        
        # Compare outputs
        if archive_output and gpu_output:
            comparison = self.compare_outputs(archive_output, gpu_output)
            self.analyze_differences(comparison)
            
            # Generate summary report
            self.generate_summary_report(comparison)
        else:
            logger.error("Failed to generate outputs from one or both systems")
            
    def generate_summary_report(self, comparison):
        """Generate a summary report."""
        
        report_path = os.path.join(self.results_dir, 'COMPARISON_SUMMARY.md')
        
        with open(report_path, 'w') as f:
            f.write("# Comprehensive Comparison Report\n\n")
            f.write(f"**Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            
            all_match = all([
                comparison.get('sheets_match', False),
                comparison.get('columns_match', False),
                comparison.get('trade_count_match', False),
                comparison.get('exit_reasons_match', False),
                comparison.get('pnl_match', False)
            ])
            
            if all_match:
                f.write("✅ **PERFECT MATCH**: Both systems produce identical results!\n\n")
            else:
                f.write("❌ **DIFFERENCES FOUND**\n\n")
                
            f.write("## Detailed Comparison\n\n")
            f.write(f"- Sheet Structure Match: {'✅' if comparison.get('sheets_match') else '❌'}\n")
            f.write(f"- Column Structure Match: {'✅' if comparison.get('columns_match') else '❌'}\n")
            f.write(f"- Trade Count Match: {'✅' if comparison.get('trade_count_match') else '❌'}\n")
            f.write(f"- Exit Reasons Match: {'✅' if comparison.get('exit_reasons_match') else '❌'}\n")
            f.write(f"- P&L Match: {'✅' if comparison.get('pnl_match') else '❌'}\n\n")
            
            details = comparison.get('details', {})
            
            f.write("## Trade Details\n\n")
            f.write(f"- Archive Trades: {details.get('archive_trades', 'N/A')}\n")
            f.write(f"- GPU Trades: {details.get('gpu_trades', 'N/A')}\n")
            f.write(f"- Archive Total P&L: {details.get('archive_total_pnl', 0):.2f}\n")
            f.write(f"- GPU Total P&L: {details.get('gpu_total_pnl', 0):.2f}\n\n")
            
            f.write("## Exit Reasons\n\n")
            f.write("### Archive System\n")
            archive_reasons = details.get('archive_exit_reasons', {})
            for reason, count in archive_reasons.items():
                f.write(f"- {reason}: {count}\n")
                
            f.write("\n### GPU System\n")
            gpu_reasons = details.get('gpu_exit_reasons', {})
            for reason, count in gpu_reasons.items():
                f.write(f"- {reason}: {count}\n")
                
        logger.info(f"\nSummary report saved to: {report_path}")


if __name__ == "__main__":
    comparison = ComprehensiveComparison()
    comparison.run()