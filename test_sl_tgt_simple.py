#!/usr/bin/env python3
"""
Simple test to verify SL/TGT functionality with actual backtest
"""

import os
import sys
import pandas as pd
import shutil
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_simple_sl_tgt_test():
    """Create a simple test case with tight SL/TGT to ensure hits."""
    
    test_dir = '/srv/samba/shared/test_results/sl_tgt_simple_test'
    os.makedirs(test_dir, exist_ok=True)
    
    # Copy a working portfolio as base
    src_portfolio = '/srv/samba/shared/INPUT PORTFOLIO.xlsx'
    test_portfolio = os.path.join(test_dir, 'test_portfolio.xlsx')
    
    if os.path.exists(src_portfolio):
        shutil.copy(src_portfolio, test_portfolio)
        logger.info(f"Copied base portfolio from {src_portfolio}")
    else:
        # Create from scratch
        portfolio_data = {
            'PortfolioSetting': pd.DataFrame([{
                'StartDate': '01_04_2024',
                'EndDate': '05_04_2024',
                'IsTickBT': 'YES',
                'Enabled': 'YES',
                'PortfolioName': 'SL_TGT_TEST',
                'PortfolioTarget': 50000,
                'PortfolioStoploss': 20000,
                'PortfolioTrailingType': 'none',
                'PnLCalTime': 152500,
                'LockPercent': 0,
                'TrailPercent': 0,
                'Multiplier': 1.0,
                'SlippagePercent': 0.05
            }]),
            'StrategySetting': pd.DataFrame([{
                'Enabled': 'YES',
                'PortfolioName': 'SL_TGT_TEST',
                'StrategyType': 'TBS',
                'StrategyExcelFilePath': os.path.join(test_dir, 'test_strategy.xlsx')
            }])
        }
        
        with pd.ExcelWriter(test_portfolio) as writer:
            for sheet, df in portfolio_data.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
    
    # Update portfolio settings
    xl = pd.ExcelFile(test_portfolio)
    with pd.ExcelWriter(test_portfolio, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        # Update dates
        df = pd.read_excel(xl, 'PortfolioSetting')
        df['StartDate'] = '01_04_2024'
        df['EndDate'] = '05_04_2024'
        df['PortfolioName'] = 'SL_TGT_TEST'
        df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        
        # Update strategy path
        df = pd.read_excel(xl, 'StrategySetting')
        df['StrategyExcelFilePath'] = os.path.join(test_dir, 'test_strategy.xlsx')
        df['PortfolioName'] = 'SL_TGT_TEST'
        df.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    # Create strategy with VERY tight SL/TGT to ensure hits
    strategy_data = {
        'GeneralParameter': pd.DataFrame([{
            'StrategyName': 'TIGHT_SL_TGT_TEST',
            'MoveSlToCost': 'no',
            'Underlying': 'SPOT',
            'Index': 'NIFTY',
            'Weekdays': '1,2,3,4,5',
            'DTE': 0,
            'StrikeSelectionTime': 91500,
            'StartTime': 91500,
            'LastEntryTime': 93000,
            'EndTime': 152500,
            'StrategyProfit': 1000,  # Very tight strategy profit
            'StrategyLoss': 500,     # Very tight strategy loss
            'StrategyProfitReExecuteNo': 0,
            'StrategyLossReExecuteNo': 0,
            'StrategyTrailingType': 'none',
            'PnLCalTime': 152500,
            'LockPercent': 0,
            'TrailPercent': 0,
            'SqOff1Time': 230000,
            'SqOff1Percent': 0,
            'SqOff2Time': 230000,
            'SqOff2Percent': 0,
            'ProfitReaches': 0,
            'LockMinProfitAt': 0,
            'IncreaseInProfit': 0,
            'TrailMinProfitBy': 0,
            'TgtTrackingFrom': 'high/low',
            'TgtRegisterPriceFrom': 'tracking',
            'SlTrackingFrom': 'high/low',
            'SlRegisterPriceFrom': 'tracking',
            'PnLCalculationFrom': 'close',
            'ConsiderHedgePnLForStgyPnL': 'no',
            'StoplossCheckingInterval': 1,
            'TargetCheckingInterval': 1,
            'ReEntryCheckingInterval': 1,
            'OnExpiryDayTradeNextExpiry': 'no'
        }]),
        'LegParameter': pd.DataFrame([
            {
                'StrategyName': 'TIGHT_SL_TGT_TEST',
                'IsIdle': 'no',
                'LegID': 1,
                'Instrument': 'call',
                'Transaction': 'sell',
                'Expiry': 'current',
                'W&Type': 'percentage',
                'W&TValue': 0,
                'TrailW&T': 'no',
                'StrikeMethod': 'atm',
                'MatchPremium': 'high',
                'StrikeValue': 0,
                'StrikePremiumCondition': '=',
                'SLType': 'percentage',
                'SLValue': 5,   # VERY tight 5% SL
                'TGTType': 'percentage',
                'TGTValue': 10,  # 10% target
                'TrailSLType': 'percentage',
                'SL_TrailAt': 0,
                'SL_TrailBy': 0,
                'Lots': 1,
                'ReEntryType': 'none',
                'ReEnteriesCount': 0,
                'OnEntry_OpenTradeOn': 0,
                'OnEntry_SqOffTradeOff': 0,
                'OnEntry_SqOffAllLegs': 'no',
                'OnEntry_OpenTradeDelay': 0,
                'OnEntry_SqOffDelay': 0,
                'OnExit_OpenTradeOn': 0,
                'OnExit_SqOffTradeOff': 0,
                'OnExit_SqOffAllLegs': 'no',
                'OnExit_OpenAllLegs': 'no',
                'OnExit_OpenTradeDelay': 0,
                'OnExit_SqOffDelay': 0,
                'OpenHedge': 'No',
                'HedgeStrikeMethod': 'atm',
                'HedgeStrikeValue': 0,
                'HedgeStrikePremiumCondition': '='
            },
            {
                'StrategyName': 'TIGHT_SL_TGT_TEST',
                'IsIdle': 'no',
                'LegID': 2,
                'Instrument': 'put',
                'Transaction': 'sell',
                'Expiry': 'current',
                'W&Type': 'percentage',
                'W&TValue': 0,
                'TrailW&T': 'no',
                'StrikeMethod': 'atm',
                'MatchPremium': 'high',
                'StrikeValue': 0,
                'StrikePremiumCondition': '=',
                'SLType': 'points',
                'SLValue': 10,   # 10 points SL
                'TGTType': 'points',
                'TGTValue': 20,  # 20 points target
                'TrailSLType': 'percentage',
                'SL_TrailAt': 0,
                'SL_TrailBy': 0,
                'Lots': 1,
                'ReEntryType': 'none',
                'ReEnteriesCount': 0,
                'OnEntry_OpenTradeOn': 0,
                'OnEntry_SqOffTradeOff': 0,
                'OnEntry_SqOffAllLegs': 'no',
                'OnEntry_OpenTradeDelay': 0,
                'OnEntry_SqOffDelay': 0,
                'OnExit_OpenTradeOn': 0,
                'OnExit_SqOffTradeOff': 0,
                'OnExit_SqOffAllLegs': 'no',
                'OnExit_OpenAllLegs': 'no',
                'OnExit_OpenTradeDelay': 0,
                'OnExit_SqOffDelay': 0,
                'OpenHedge': 'No',
                'HedgeStrikeMethod': 'atm',
                'HedgeStrikeValue': 0,
                'HedgeStrikePremiumCondition': '='
            }
        ])
    }
    
    strategy_file = os.path.join(test_dir, 'test_strategy.xlsx')
    with pd.ExcelWriter(strategy_file) as writer:
        for sheet, df in strategy_data.items():
            df.to_excel(writer, sheet_name=sheet, index=False)
    
    logger.info(f"Created test files in {test_dir}")
    logger.info(f"Portfolio: {test_portfolio}")
    logger.info(f"Strategy: {strategy_file}")
    
    return test_portfolio, strategy_file


def run_and_analyze_backtest(portfolio_file):
    """Run backtest and analyze results."""
    
    output_path = portfolio_file.replace('_portfolio.xlsx', '_output.xlsx')
    
    # Run GPU backtest
    env = os.environ.copy()
    env['USE_GOLDEN_FORMAT'] = 'true'
    
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_path,
        '--debug'
    ]
    
    logger.info("\n=== Running GPU Backtest ===")
    logger.info(f"Command: {' '.join(cmd)}")
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env, cwd='/srv/samba/shared')
    
    if result.returncode == 0:
        logger.info("✅ Backtest completed successfully")
        
        # Analyze output
        if os.path.exists(output_path):
            xl = pd.ExcelFile(output_path)
            logger.info(f"\nOutput sheets: {xl.sheet_names}")
            
            if 'PORTFOLIO Trans' in xl.sheet_names:
                df = pd.read_excel(xl, 'PORTFOLIO Trans')
                logger.info(f"\nTotal trades: {len(df)}")
                
                if len(df) > 0 and 'Reason' in df.columns:
                    logger.info("\nExit Reasons:")
                    reason_counts = df['Reason'].value_counts()
                    for reason, count in reason_counts.items():
                        logger.info(f"  {reason}: {count}")
                    
                    # Show sample trades
                    logger.info("\nSample trades:")
                    cols_to_show = ['Strategy Name', 'Strike', 'CE/PE', 'Entry at', 'Exit at.1', 'Points', 'Net PNL', 'Reason']
                    available_cols = [c for c in cols_to_show if c in df.columns]
                    if available_cols:
                        logger.info(df[available_cols].head())
                else:
                    logger.warning("No trades found or 'Reason' column missing")
            else:
                logger.warning("No PORTFOLIO Trans sheet found")
        else:
            logger.error("Output file not created")
    else:
        logger.error(f"❌ Backtest failed with return code: {result.returncode}")
        if result.stderr:
            logger.error("Error output:")
            logger.error(result.stderr[-1000:])  # Last 1000 chars
    
    return output_path if os.path.exists(output_path) else None


def main():
    logger.info("=" * 80)
    logger.info("Simple SL/TGT Test with Actual Backtest")
    logger.info("=" * 80)
    
    # Create test files
    portfolio_file, strategy_file = create_simple_sl_tgt_test()
    
    # Run backtest
    output = run_and_analyze_backtest(portfolio_file)
    
    if output:
        logger.info(f"\n✅ Test completed successfully!")
        logger.info(f"Output file: {output}")
        
        # Check for SL/TGT hits in simulated data
        logger.info("\n=== Checking Simulated SL/TGT Output ===")
        simulated_file = '/srv/samba/shared/test_results/comprehensive_sl_tgt_test/sl_tgt_scenarios_golden.xlsx'
        if os.path.exists(simulated_file):
            xl = pd.ExcelFile(simulated_file)
            df = pd.read_excel(xl, 'PORTFOLIO Trans')
            logger.info("\nSimulated Exit Reasons:")
            logger.info(df['Reason'].value_counts())
    else:
        logger.error("\n❌ Test failed")


if __name__ == "__main__":
    main()