#!/usr/bin/env python3
"""
Comprehensive UI Testing with Playwright
========================================

This script performs comprehensive UI testing including:
1. Login flow testing
2. File upload testing
3. Backtest execution testing
4. Progress monitoring
5. Results validation

Usage:
    python comprehensive_ui_testing.py --mode all
    python comprehensive_ui_testing.py --mode login
    python comprehensive_ui_testing.py --mode upload
"""

import os
import sys
import asyncio
import logging
import json
import time
from pathlib import Path
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser, BrowserContext

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/srv/samba/shared/logs/comprehensive_ui_testing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveUITester:
    """Comprehensive UI testing with <PERSON><PERSON>."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results_path = Path('/srv/samba/shared/test_results/ui_testing')
        self.results_path.mkdir(parents=True, exist_ok=True)
        
        # Test configuration
        self.test_files = {
            'oi_input': '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/input_maxoi.xlsx',
            'tbs_portfolio': '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/TBS_PORTFOLIO.xlsx',
            'tbs_strategy': '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/TBS_STRATEGY.xlsx'
        }
        
        # Test results storage
        self.test_results = {
            'login_test': {},
            'upload_test': {},
            'backtest_test': {},
            'progress_test': {},
            'results_test': {}
        }
        
        self.browser = None
        self.context = None
        self.page = None
    
    async def setup_browser(self):
        """Setup Playwright browser."""
        logger.info("Setting up Playwright browser...")
        
        try:
            playwright = await async_playwright().start()
            
            # Launch browser with specific options
            self.browser = await playwright.chromium.launch(
                headless=True,  # Headless mode for server environment
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--allow-running-insecure-content',
                    '--disable-gpu',
                    '--disable-software-rasterizer'
                ]
            )
            
            # Create context
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
            )
            
            # Create page
            self.page = await self.context.new_page()
            
            # Set timeouts
            self.page.set_default_timeout(30000)  # 30 seconds
            
            logger.info("✓ Browser setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Browser setup failed: {e}")
            return False
    
    async def cleanup_browser(self):
        """Cleanup browser resources."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            logger.info("✓ Browser cleanup completed")
        except Exception as e:
            logger.error(f"Browser cleanup failed: {e}")
    
    async def test_login_flow(self):
        """Test complete login flow."""
        logger.info("\n=== Testing Login Flow ===")
        
        test_result = {
            'status': 'PENDING',
            'steps': [],
            'screenshots': [],
            'errors': []
        }
        
        try:
            # Step 1: Navigate to login page
            logger.info("Step 1: Navigating to login page...")
            await self.page.goto(f"{self.base_url}/login")
            await self.page.wait_for_load_state('networkidle')
            
            # Take screenshot
            screenshot_path = self.results_path / f'login_step1_{datetime.now().strftime("%H%M%S")}.png'
            await self.page.screenshot(path=screenshot_path)
            test_result['screenshots'].append(str(screenshot_path))
            
            test_result['steps'].append({
                'step': 'navigate_to_login',
                'status': 'PASSED',
                'url': self.page.url
            })
            
            # Step 2: Check login form elements
            logger.info("Step 2: Checking login form elements...")
            
            # Check for phone input
            phone_input = await self.page.query_selector('input[type="tel"], input[placeholder*="phone"], #phone')
            if phone_input:
                logger.info("✓ Phone input field found")
                test_result['steps'].append({
                    'step': 'phone_input_found',
                    'status': 'PASSED'
                })
            else:
                logger.warning("✗ Phone input field not found")
                test_result['steps'].append({
                    'step': 'phone_input_found',
                    'status': 'FAILED'
                })
            
            # Check for login button
            login_button = await self.page.query_selector('button[type="submit"], button:has-text("Login"), button:has-text("Send OTP")')
            if login_button:
                logger.info("✓ Login button found")
                test_result['steps'].append({
                    'step': 'login_button_found',
                    'status': 'PASSED'
                })
            else:
                logger.warning("✗ Login button not found")
                test_result['steps'].append({
                    'step': 'login_button_found',
                    'status': 'FAILED'
                })
            
            # Step 3: Test phone number input
            logger.info("Step 3: Testing phone number input...")
            if phone_input:
                await phone_input.fill('9876543210')
                await self.page.wait_for_timeout(1000)
                
                # Take screenshot
                screenshot_path = self.results_path / f'login_step3_{datetime.now().strftime("%H%M%S")}.png'
                await self.page.screenshot(path=screenshot_path)
                test_result['screenshots'].append(str(screenshot_path))
                
                test_result['steps'].append({
                    'step': 'phone_number_input',
                    'status': 'PASSED',
                    'value': '9876543210'
                })
            
            # Step 4: Test login button click (without actually sending OTP)
            logger.info("Step 4: Testing login button interaction...")
            if login_button:
                # Just hover over the button to test interaction
                await login_button.hover()
                await self.page.wait_for_timeout(500)
                
                test_result['steps'].append({
                    'step': 'login_button_interaction',
                    'status': 'PASSED'
                })
            
            test_result['status'] = 'PASSED'
            logger.info("✓ Login flow test completed successfully")
            
        except Exception as e:
            test_result['status'] = 'FAILED'
            test_result['errors'].append(str(e))
            logger.error(f"Login flow test failed: {e}")
        
        self.test_results['login_test'] = test_result
        return test_result
    
    async def test_file_upload_flow(self):
        """Test file upload functionality."""
        logger.info("\n=== Testing File Upload Flow ===")
        
        test_result = {
            'status': 'PENDING',
            'steps': [],
            'screenshots': [],
            'errors': []
        }
        
        try:
            # Step 1: Navigate to main dashboard
            logger.info("Step 1: Navigating to main dashboard...")
            await self.page.goto(f"{self.base_url}/")
            await self.page.wait_for_load_state('networkidle')
            
            # Take screenshot
            screenshot_path = self.results_path / f'upload_step1_{datetime.now().strftime("%H%M%S")}.png'
            await self.page.screenshot(path=screenshot_path)
            test_result['screenshots'].append(str(screenshot_path))
            
            test_result['steps'].append({
                'step': 'navigate_to_dashboard',
                'status': 'PASSED',
                'url': self.page.url
            })
            
            # Step 2: Look for file upload areas
            logger.info("Step 2: Checking file upload areas...")
            
            # Check for drag-drop areas
            upload_areas = await self.page.query_selector_all('.upload-area, .drop-zone, input[type="file"]')
            if upload_areas:
                logger.info(f"✓ Found {len(upload_areas)} upload areas")
                test_result['steps'].append({
                    'step': 'upload_areas_found',
                    'status': 'PASSED',
                    'count': len(upload_areas)
                })
            else:
                logger.warning("✗ No upload areas found")
                test_result['steps'].append({
                    'step': 'upload_areas_found',
                    'status': 'FAILED'
                })
            
            # Step 3: Test strategy type selection
            logger.info("Step 3: Testing strategy type selection...")
            
            # Look for strategy buttons or dropdowns
            strategy_buttons = await self.page.query_selector_all('button:has-text("OI"), button:has-text("TBS"), button:has-text("TV"), button:has-text("ORB")')
            if strategy_buttons:
                logger.info(f"✓ Found {len(strategy_buttons)} strategy buttons")
                
                # Click on OI strategy if available
                for button in strategy_buttons:
                    text = await button.text_content()
                    if 'OI' in text.upper():
                        await button.click()
                        await self.page.wait_for_timeout(1000)
                        logger.info("✓ Clicked OI strategy button")
                        break
                
                test_result['steps'].append({
                    'step': 'strategy_selection',
                    'status': 'PASSED',
                    'count': len(strategy_buttons)
                })
            else:
                logger.warning("✗ No strategy buttons found")
                test_result['steps'].append({
                    'step': 'strategy_selection',
                    'status': 'FAILED'
                })
            
            # Step 4: Test file input (if available)
            logger.info("Step 4: Testing file input...")
            
            file_inputs = await self.page.query_selector_all('input[type="file"]')
            if file_inputs and os.path.exists(self.test_files['oi_input']):
                # Test file upload with OI input file
                await file_inputs[0].set_input_files(self.test_files['oi_input'])
                await self.page.wait_for_timeout(2000)
                
                logger.info("✓ File upload test completed")
                test_result['steps'].append({
                    'step': 'file_upload_test',
                    'status': 'PASSED',
                    'file': self.test_files['oi_input']
                })
                
                # Take screenshot after upload
                screenshot_path = self.results_path / f'upload_step4_{datetime.now().strftime("%H%M%S")}.png'
                await self.page.screenshot(path=screenshot_path)
                test_result['screenshots'].append(str(screenshot_path))
            
            test_result['status'] = 'PASSED'
            logger.info("✓ File upload flow test completed successfully")
            
        except Exception as e:
            test_result['status'] = 'FAILED'
            test_result['errors'].append(str(e))
            logger.error(f"File upload flow test failed: {e}")
        
        self.test_results['upload_test'] = test_result
        return test_result
    
    async def test_backtest_execution_flow(self):
        """Test backtest execution flow."""
        logger.info("\n=== Testing Backtest Execution Flow ===")
        
        test_result = {
            'status': 'PENDING',
            'steps': [],
            'screenshots': [],
            'errors': []
        }
        
        try:
            # Step 1: Look for run/execute buttons
            logger.info("Step 1: Looking for execution buttons...")
            
            run_buttons = await self.page.query_selector_all('button:has-text("Run"), button:has-text("Execute"), button:has-text("Start"), button[id*="run"], button[class*="run"]')
            if run_buttons:
                logger.info(f"✓ Found {len(run_buttons)} execution buttons")
                test_result['steps'].append({
                    'step': 'execution_buttons_found',
                    'status': 'PASSED',
                    'count': len(run_buttons)
                })
                
                # Test button interaction (hover only)
                await run_buttons[0].hover()
                await self.page.wait_for_timeout(500)
                
            else:
                logger.warning("✗ No execution buttons found")
                test_result['steps'].append({
                    'step': 'execution_buttons_found',
                    'status': 'FAILED'
                })
            
            # Step 2: Check for progress indicators
            logger.info("Step 2: Checking for progress indicators...")
            
            progress_elements = await self.page.query_selector_all('.progress, .progress-bar, #progress, [class*="progress"]')
            if progress_elements:
                logger.info(f"✓ Found {len(progress_elements)} progress elements")
                test_result['steps'].append({
                    'step': 'progress_indicators_found',
                    'status': 'PASSED',
                    'count': len(progress_elements)
                })
            else:
                logger.warning("✗ No progress indicators found")
                test_result['steps'].append({
                    'step': 'progress_indicators_found',
                    'status': 'FAILED'
                })
            
            # Step 3: Check for log/output areas
            logger.info("Step 3: Checking for log/output areas...")
            
            log_areas = await self.page.query_selector_all('#logs, .logs, .output, .console, textarea[readonly]')
            if log_areas:
                logger.info(f"✓ Found {len(log_areas)} log areas")
                test_result['steps'].append({
                    'step': 'log_areas_found',
                    'status': 'PASSED',
                    'count': len(log_areas)
                })
            else:
                logger.warning("✗ No log areas found")
                test_result['steps'].append({
                    'step': 'log_areas_found',
                    'status': 'FAILED'
                })
            
            # Take final screenshot
            screenshot_path = self.results_path / f'backtest_final_{datetime.now().strftime("%H%M%S")}.png'
            await self.page.screenshot(path=screenshot_path)
            test_result['screenshots'].append(str(screenshot_path))
            
            test_result['status'] = 'PASSED'
            logger.info("✓ Backtest execution flow test completed successfully")
            
        except Exception as e:
            test_result['status'] = 'FAILED'
            test_result['errors'].append(str(e))
            logger.error(f"Backtest execution flow test failed: {e}")
        
        self.test_results['backtest_test'] = test_result
        return test_result
    
    async def run_comprehensive_ui_tests(self):
        """Run all comprehensive UI tests."""
        logger.info("Starting Comprehensive UI Testing...")
        
        # Setup browser
        if not await self.setup_browser():
            logger.error("Failed to setup browser")
            return False
        
        try:
            # Run all tests
            await self.test_login_flow()
            await self.test_file_upload_flow()
            await self.test_backtest_execution_flow()
            
            # Generate report
            self.generate_comprehensive_report()
            
            return True
            
        finally:
            await self.cleanup_browser()
    
    def generate_comprehensive_report(self):
        """Generate comprehensive UI testing report."""
        report = {
            'test_execution_date': datetime.now().isoformat(),
            'base_url': self.base_url,
            'test_results': self.test_results,
            'summary': {
                'total_tests': len(self.test_results),
                'passed_tests': len([t for t in self.test_results.values() if t.get('status') == 'PASSED']),
                'failed_tests': len([t for t in self.test_results.values() if t.get('status') == 'FAILED']),
                'total_screenshots': sum(len(t.get('screenshots', [])) for t in self.test_results.values())
            }
        }
        
        # Save report
        report_file = self.results_path / f'comprehensive_ui_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"\n=== COMPREHENSIVE UI TESTING REPORT ===")
        logger.info(f"Report saved: {report_file}")
        logger.info(f"Total Tests: {report['summary']['total_tests']}")
        logger.info(f"Passed: {report['summary']['passed_tests']}")
        logger.info(f"Failed: {report['summary']['failed_tests']}")
        logger.info(f"Screenshots: {report['summary']['total_screenshots']}")
        
        # Log individual test results
        for test_name, result in self.test_results.items():
            status_icon = "✓" if result.get('status') == 'PASSED' else "✗"
            logger.info(f"{status_icon} {test_name.replace('_', ' ').title()}: {result.get('status', 'UNKNOWN')}")
        
        return report_file


async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Comprehensive UI Testing')
    parser.add_argument('--mode', choices=['all', 'login', 'upload', 'backtest'], 
                       default='all', help='Test mode to run')
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = ComprehensiveUITester()
    
    if args.mode == 'all':
        success = await tester.run_comprehensive_ui_tests()
        sys.exit(0 if success else 1)
    else:
        # Setup browser for individual tests
        if not await tester.setup_browser():
            sys.exit(1)
        
        try:
            if args.mode == 'login':
                result = await tester.test_login_flow()
            elif args.mode == 'upload':
                result = await tester.test_file_upload_flow()
            elif args.mode == 'backtest':
                result = await tester.test_backtest_execution_flow()
            
            success = result.get('status') == 'PASSED'
            sys.exit(0 if success else 1)
            
        finally:
            await tester.cleanup_browser()


if __name__ == '__main__':
    asyncio.run(main())
