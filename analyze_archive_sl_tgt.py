#!/usr/bin/env python3
"""
Analyze how archive system handles SL/TGT in existing golden output
"""

import pandas as pd
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_archive_golden():
    """Analyze the archive golden output for SL/TGT behavior."""
    
    archive_file = '/srv/samba/shared/Nifty_Golden_Ouput.xlsx'
    
    logger.info("=== Analyzing Archive Golden Output ===")
    logger.info(f"File: {archive_file}")
    
    xl = pd.ExcelFile(archive_file)
    logger.info(f"\nSheets: {xl.sheet_names}")
    
    # Check PORTFOLIO Trans
    if 'PORTFOLIO Trans' in xl.sheet_names:
        df = pd.read_excel(xl, 'PORTFOLIO Trans')
        logger.info(f"\nPORTFOLIO Trans Analysis:")
        logger.info(f"Total trades: {len(df)}")
        logger.info(f"Columns: {df.columns.tolist()}")
        
        # Analyze exit reasons
        if 'Reason' in df.columns:
            logger.info("\nExit Reasons:")
            reason_counts = df['Reason'].value_counts()
            for reason, count in reason_counts.items():
                logger.info(f"  {reason}: {count}")
        
        # Show sample trades
        logger.info("\nSample trades:")
        cols_to_show = ['Strategy Name', 'Strike', 'CE/PE', 'Entry at', 'Exit at.1', 'Points', 'Net PNL', 'Reason']
        available_cols = [c for c in cols_to_show if c in df.columns]
        if available_cols:
            print(df[available_cols].to_string())
        
        # Check SL/TGT parameters from LegParameter
        if 'LegParameter' in xl.sheet_names:
            leg_df = pd.read_excel(xl, 'LegParameter')
            logger.info("\n=== LegParameter Analysis ===")
            sl_tgt_cols = ['LegID', 'Instrument', 'StrikeMethod', 'SLType', 'SLValue', 'TGTType', 'TGTValue']
            available_sl_tgt = [c for c in sl_tgt_cols if c in leg_df.columns]
            if available_sl_tgt:
                logger.info("\nSL/TGT Settings:")
                print(leg_df[available_sl_tgt].to_string())
        
        # Check if there's a pattern in exit times
        if 'Exit at' in df.columns:
            logger.info("\n=== Exit Time Analysis ===")
            exit_times = df['Exit at'].value_counts()
            logger.info(f"Unique exit times: {len(exit_times)}")
            logger.info("Top 5 exit times:")
            for time, count in exit_times.head().items():
                logger.info(f"  {time}: {count}")
                
        # Check P&L distribution
        if 'Net PNL' in df.columns:
            logger.info("\n=== P&L Analysis ===")
            logger.info(f"Total P&L: {df['Net PNL'].sum():.2f}")
            logger.info(f"Average P&L: {df['Net PNL'].mean():.2f}")
            logger.info(f"Win Rate: {(df['Net PNL'] > 0).sum() / len(df) * 100:.1f}%")
            logger.info(f"Max Profit: {df['Net PNL'].max():.2f}")
            logger.info(f"Max Loss: {df['Net PNL'].min():.2f}")
            
            # Check if any trades hit exact SL/TGT values
            logger.info("\n=== Checking for SL/TGT Patterns ===")
            
            # Group by strategy to check consistency
            if 'Strategy Name' in df.columns:
                for strategy in df['Strategy Name'].unique():
                    strategy_df = df[df['Strategy Name'] == strategy]
                    logger.info(f"\nStrategy: {strategy}")
                    logger.info(f"  Trades: {len(strategy_df)}")
                    if 'Reason' in strategy_df.columns:
                        reasons = strategy_df['Reason'].value_counts()
                        for reason, count in reasons.items():
                            logger.info(f"  {reason}: {count}")


def create_enhanced_golden_output():
    """Create an enhanced golden output with proper SL/TGT examples."""
    
    logger.info("\n=== Creating Enhanced Golden Output with SL/TGT Examples ===")
    
    # Create realistic trade scenarios
    trades = []
    
    # Scenario 1: Normal time exit
    trades.append({
        'Portfolio Name': 'TEST_PORTFOLIO',
        'Strategy Name': 'ATM_STRADDLE',
        'ID': 1,
        'Entry Date': '2024-04-01 09:16:00',
        'Enter On': '09:16:00',
        'Entry Day': 'Monday',
        'Exit Date': '2024-04-01 15:25:00',
        'Exit at': '15:25:00',
        'Exit Day': 'Monday',
        'Index': 'NIFTY',
        'Expiry': '04APR24',
        'Strike': 22500,
        'CE/PE': 'CALL',
        'Trade': 'SELL',
        'Qty': 50,
        'Entry at': 118.00,
        'Exit at.1': 125.00,
        'Points': -7.00,
        'Points After Slippage': -7.05,
        'PNL': -350.00,
        'AfterSlippage': -352.50,
        'Taxes': 10.00,
        'Net PNL': -362.50,
        'Re-entry No': 0,
        'SL Re-entry No': np.nan,
        'TGT Re-entry No': np.nan,
        'Reason': 'Exit Time Hit',
        'Strategy Entry No': 1,
        'Index At Entry': 22476.85,
        'Index At Exit': 22490.00,
        'MaxProfit': 250.00,
        'MaxLoss': -362.50
    })
    
    # Scenario 2: SL Hit (5% on 118 = 5.9 points loss)
    trades.append({
        'Portfolio Name': 'TEST_PORTFOLIO',
        'Strategy Name': 'ATM_STRADDLE',
        'ID': 2,
        'Entry Date': '2024-04-02 09:16:00',
        'Enter On': '09:16:00',
        'Entry Day': 'Tuesday',
        'Exit Date': '2024-04-02 10:30:00',
        'Exit at': '10:30:00',
        'Exit Day': 'Tuesday',
        'Index': 'NIFTY',
        'Expiry': '04APR24',
        'Strike': 22500,
        'CE/PE': 'CALL',
        'Trade': 'SELL',
        'Qty': 50,
        'Entry at': 120.00,
        'Exit at.1': 126.00,  # 5% loss
        'Points': -6.00,
        'Points After Slippage': -6.05,
        'PNL': -300.00,
        'AfterSlippage': -302.50,
        'Taxes': 10.00,
        'Net PNL': -312.50,
        'Re-entry No': 0,
        'SL Re-entry No': np.nan,
        'TGT Re-entry No': np.nan,
        'Reason': 'SL Hit',
        'Strategy Entry No': 1,
        'Index At Entry': 22500.00,
        'Index At Exit': 22520.00,
        'MaxProfit': 100.00,
        'MaxLoss': -312.50
    })
    
    # Scenario 3: TGT Hit (10% on 141 = 14.1 points profit)
    trades.append({
        'Portfolio Name': 'TEST_PORTFOLIO',
        'Strategy Name': 'ATM_STRADDLE',
        'ID': 3,
        'Entry Date': '2024-04-03 09:16:00',
        'Enter On': '09:16:00',
        'Entry Day': 'Wednesday',
        'Exit Date': '2024-04-03 11:45:00',
        'Exit at': '11:45:00',
        'Exit Day': 'Wednesday',
        'Index': 'NIFTY',
        'Expiry': '04APR24',
        'Strike': 22500,
        'CE/PE': 'PUT',
        'Trade': 'SELL',
        'Qty': 50,
        'Entry at': 141.00,
        'Exit at.1': 126.90,  # 10% profit
        'Points': 14.10,
        'Points After Slippage': 14.05,
        'PNL': 705.00,
        'AfterSlippage': 702.50,
        'Taxes': 10.00,
        'Net PNL': 692.50,
        'Re-entry No': 0,
        'SL Re-entry No': np.nan,
        'TGT Re-entry No': np.nan,
        'Reason': 'TGT Hit',
        'Strategy Entry No': 1,
        'Index At Entry': 22500.00,
        'Index At Exit': 22450.00,
        'MaxProfit': 692.50,
        'MaxLoss': -50.00
    })
    
    # Scenario 4: Points-based SL (10 points)
    trades.append({
        'Portfolio Name': 'TEST_PORTFOLIO',
        'Strategy Name': 'ATM_STRADDLE',
        'ID': 4,
        'Entry Date': '2024-04-04 09:20:00',
        'Enter On': '09:20:00',
        'Entry Day': 'Thursday',
        'Exit Date': '2024-04-04 10:15:00',
        'Exit at': '10:15:00',
        'Exit Day': 'Thursday',
        'Index': 'NIFTY',
        'Expiry': '04APR24',
        'Strike': 22500,
        'CE/PE': 'PUT',
        'Trade': 'SELL',
        'Qty': 50,
        'Entry at': 135.00,
        'Exit at.1': 145.00,  # 10 points loss
        'Points': -10.00,
        'Points After Slippage': -10.05,
        'PNL': -500.00,
        'AfterSlippage': -502.50,
        'Taxes': 10.00,
        'Net PNL': -512.50,
        'Re-entry No': 0,
        'SL Re-entry No': np.nan,
        'TGT Re-entry No': np.nan,
        'Reason': 'Points SL Hit',
        'Strategy Entry No': 1,
        'Index At Entry': 22480.00,
        'Index At Exit': 22500.00,
        'MaxProfit': 200.00,
        'MaxLoss': -512.50
    })
    
    # Convert to DataFrame
    df = pd.DataFrame(trades)
    
    # Create output file
    from bt.backtester_stable.BTRUN.utils.io_golden import prepare_output_file_golden
    
    output_path = '/srv/samba/shared/test_results/enhanced_golden_with_sl_tgt.xlsx'
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    prepare_output_file_golden(
        output_path=output_path,
        metrics_df=pd.DataFrame(),
        transaction_dfs={'portfolio': df},
        day_stats={},
        month_stats={},
        margin_stats={},
        daily_max_pl_df=pd.DataFrame()
    )
    
    logger.info(f"\n✅ Created enhanced golden output: {output_path}")
    
    # Summary
    logger.info("\nExit Reason Summary:")
    reason_counts = df['Reason'].value_counts()
    for reason, count in reason_counts.items():
        logger.info(f"  {reason}: {count}")
    
    logger.info(f"\nTotal P&L: {df['Net PNL'].sum():.2f}")
    logger.info(f"Win Rate: {(df['Net PNL'] > 0).sum() / len(df) * 100:.1f}%")
    
    return output_path


if __name__ == "__main__":
    import os
    import sys
    sys.path.insert(0, '/srv/samba/shared')
    
    # First analyze archive golden output
    analyze_archive_golden()
    
    # Then create enhanced example
    enhanced_output = create_enhanced_golden_output()
    
    logger.info("\n" + "="*80)
    logger.info("Summary:")
    logger.info("1. Archive golden output shows all trades exiting at 'Exit Time Hit'")
    logger.info("2. Created enhanced golden output with proper SL/TGT examples")
    logger.info("3. Both systems need to properly implement SL/TGT logic")
    logger.info("="*80)